package com.uino.oauth.common.bean;

import java.io.Serializable;

import lombok.Data;

@Data
public class UinoSessionCache implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     * 
     */
    private Long id;

    /**
     * 唯一标识
     */
    private String sessionId;

    /**
     * 保存请求的字节流
     */
    private byte[] savedRequestBytes;

    /** 所属域 */
    private Long domainId;

    /** 创建人 */
    private String creator;

    /** 修改人 */
    private String modifier;

    /** 创建时间 */
    private Long createTime;

    /** 修改时间 */
    private Long modifyTime;
}

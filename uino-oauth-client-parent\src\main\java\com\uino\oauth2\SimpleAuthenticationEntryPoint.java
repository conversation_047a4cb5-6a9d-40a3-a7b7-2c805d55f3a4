package com.uino.oauth2;

import java.io.IOException;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 鉴权失败的处理
 *
 * @author: weixuesong
 * @create: 2020/08/06 15:23
 **/
@Data
@EqualsAndHashCode(callSuper = false)
public class SimpleAuthenticationEntryPoint implements AuthenticationEntryPoint {

	private String redirectAuthPrefix;

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
            AuthenticationException authException) throws IOException, ServletException {
        String request_header = request.getHeader("REQUEST_HEADER");
        if (request_header == null || !"binary-http-client-header".equalsIgnoreCase(request_header)) {
        	String redirectAuth = "/redirectAuth";
        	if(redirectAuthPrefix!=null) {
        		redirectAuth = redirectAuthPrefix.endsWith("/")?redirectAuthPrefix+"redirectAuth":redirectAuthPrefix+"/redirectAuth";
        	}
            response.sendRedirect(redirectAuth);
        } else {
            // 返回401未授权状态码
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, authException.getMessage());
        }
    }
}

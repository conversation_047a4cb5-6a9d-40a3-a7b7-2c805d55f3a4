package com.uino.oauth.common.service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import com.uino.api.client.permission.IOauthApiSvc;
import com.uino.bean.permission.base.OauthTokenDetail;
import com.uino.util.cache.ICacheService;
import jodd.system.SystemUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtEncoder;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

/**
 * 令牌存储服务
 * 在Spring Security 6中替代原有的OAuth2 TokenStore
 */
@Service
@Slf4j
public class UinoTokenStore {

    @Autowired
    private IOauthApiSvc oauthApiSvc;

    @Autowired(required = false)
    private JwtDecoder jwtDecoder;

    String UINO_USER_ONLINE_TOKEN_PREFIX = "uino:oauth:token";

    @Autowired
    private ICacheService cacheService;

    /**
     * 读取访问令牌
     * @param tokenValue 令牌值
     * @return JWT令牌
     */
    public Jwt readAccessToken(String tokenValue) {
        try {
            log.debug("开始解码JWT令牌: {}", tokenValue);
            if (jwtDecoder == null) {
                log.error("JWT解码器未配置");
                throw new IllegalStateException("JWT解码器未配置");
            }

            Jwt jwt = jwtDecoder.decode(tokenValue);
            log.debug("JWT令牌解码成功，主题: {}, 发行者: {}", jwt.getSubject(), jwt.getIssuer());
            return jwt;
        } catch (Exception e) {
            log.error("JWT令牌解码失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 移除访问令牌
     * @param token JWT令牌或字符串令牌
     */
    public void removeAccessToken(Object token) {
        String tokenCode;
        if (token instanceof Jwt) {
            tokenCode = ((Jwt) token).getTokenValue();
        } else if (token instanceof String) {
            tokenCode = (String) token;
        } else {
            // 尝试获取值方法
            try {
                tokenCode = token.getClass().getMethod("getValue").invoke(token).toString();
            } catch (Exception e) {
                // 无法获取令牌值
                return;
            }
        }
        oauthApiSvc.delByCode(tokenCode);
        if (cacheService.getCache(UINO_USER_ONLINE_TOKEN_PREFIX + ":" + tokenCode) != null) {
            cacheService.delKey(UINO_USER_ONLINE_TOKEN_PREFIX + ":" + tokenCode);
        }
    }

    /**
     * 获取访问令牌
     * @param authentication 认证信息
     * @return JWT令牌
     */
    public Jwt getAccessToken(Authentication authentication) {
        // 在Spring Security 6中，需要实现从认证对象获取令牌的逻辑
        // 这里是一个简化的实现
        return null;
    }

    /**
     * 读取认证信息
     * @param tokenCode 令牌代码
     * @return 认证信息
     */
    public Authentication readAuthentication(String tokenCode) {

        OauthTokenDetail tokenDetail = cacheService.getCacheByType(UINO_USER_ONLINE_TOKEN_PREFIX + ":" + tokenCode, OauthTokenDetail.class);
        boolean sourceRedis = true;
        if (tokenDetail == null) {
            tokenDetail = oauthApiSvc.getTokenDetailByCode(tokenCode);
            sourceRedis = false;
        }
        long nowMills = System.currentTimeMillis();
        if (tokenDetail != null && tokenDetail.getExpirationTime() > nowMills) {
            if (!sourceRedis) {
                cacheService.setCache(UINO_USER_ONLINE_TOKEN_PREFIX + ":" + tokenCode, tokenDetail, 30 * 60 * 1000L);
            }
            try {
                // 使用ObjectInputStream进行反序列化
                byte[] authBytes = tokenDetail.getAuthentication();
                if (authBytes != null) {
                    try (ByteArrayInputStream bis = new ByteArrayInputStream(authBytes);
                         ObjectInputStream ois = new ObjectInputStream(bis)) {
                        return (Authentication) ois.readObject();
                    } catch (IOException | ClassNotFoundException e) {
                        log.warn("反序列化认证信息失败: {}", e.getMessage());
                        return null;
                    }
                }
            } catch (Exception e) {
                log.warn("反序列化认证信息失败: {}", e.getMessage());
                return null;
            }
        }
        return null;
    }

    /**
     * 从jdbcTokenStore复制过来的，暂时其实也没提供刷新token，先这样用着，等调到这再说
     *
     * @param value
     * @return
     */
    protected String extractTokenKey(String value) {
        if (value == null) {
            return null;
        }
        MessageDigest digest;
        try {
            digest = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            throw new IllegalStateException("MD5 algorithm not available.  Fatal (should be in the JDK).");
        }

        try {
            byte[] bytes = digest.digest(value.getBytes("UTF-8"));
            return String.format("%032x", new BigInteger(1, bytes));
        } catch (UnsupportedEncodingException e) {
            throw new IllegalStateException("UTF-8 encoding not available.  Fatal (should be in the JDK).");
        }
    }
}

---
# 授权认证模块(之后简称为oauth)****
---
##模块简介
###uino-oauth-common
####简介
	oauth的基础模块，封装了依赖bean以及自定义部分服务(之后可能将bean与服务类拆分)
	
###uino-oauth-server
####简介
	oauth的授权认证服务，不持有任何资源，只负责进行用户鉴权授权等工作

###uino-oauth-client-parent
####简介
	oauth的客户端模板，帮助各客户端/资源端快速集成，具体使用方式见该模块readme
	
###uino-oauth-demo-client01/uino-oauth-demo-resource01
####简介
	两个demo模块，供调试参考使用，之后会从中移除
	
##客户端/资源端集成方式
###见uino-oauth-client-parent下的readme
	
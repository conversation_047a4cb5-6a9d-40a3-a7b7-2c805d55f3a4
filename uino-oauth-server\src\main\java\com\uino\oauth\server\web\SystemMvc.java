package com.uino.oauth.server.web;

import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.api.client.sys.ILogoApiSvc;
import com.uino.api.client.sys.ITenantDomainApiSvc;
import com.uino.bean.sys.base.Logo;
import com.uino.bean.sys.base.TenantDomain;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.binary.framework.util.ControllerUtils;

import lombok.extern.slf4j.Slf4j;

@RequestMapping("/sys")
@RestController
@Slf4j
public class SystemMvc {

	@Value("${uino.tenantDomain:false}")
	private boolean isOpenTenantDomain;

	@Autowired
	private ILogoApiSvc logoApi;

	@Autowired
	private ITenantDomainApiSvc tenantDomainApiSvc;

	@PostMapping("getLogos")
	public void getLogos(HttpServletRequest request, HttpServletResponse response) {
		Map<String, Logo> res = logoApi.getLogos();
		ControllerUtils.returnJson(request, response, res);
	}
	@ApiOperation("是否开启多租户")
	@PostMapping("/multiTenantStatus")
	public void multiTenantStatus(HttpServletRequest request, HttpServletResponse response) {
		ControllerUtils.returnJson(request, response, isOpenTenantDomain);
	}

	@ApiOperation("查询所有可用的域")
	@PostMapping("/queryList")
	public void queryList(HttpServletRequest request, HttpServletResponse response) {
		List<TenantDomain> tenantDomains = tenantDomainApiSvc.queryAvailableList();
		ControllerUtils.returnJson(request, response, tenantDomains);
	}
}

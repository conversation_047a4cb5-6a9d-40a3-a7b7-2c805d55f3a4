package com.uino.oauth.server.init;

import com.uino.api.client.permission.IOauthApiSvc;
import com.uino.bean.permission.base.OauthTokenDetail;
import com.uino.oauth.common.bean.UinoUserDetails;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.core.ClaimAccessor;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.core.endpoint.OAuth2ParameterNames;
import org.springframework.security.oauth2.server.authorization.OAuth2TokenType;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.settings.OAuth2TokenFormat;
import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenClaimsSet;
import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenContext;
import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenGenerator;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.ObjectOutputStream;
import java.time.Instant;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025/5/7 15:07
 */
@Slf4j
public class UUIDOAuth2TokenGenerator implements OAuth2TokenGenerator<OAuth2AccessToken> {

    private IOauthApiSvc oauthApiSvc;

    public UUIDOAuth2TokenGenerator(IOauthApiSvc oauthApiSvc) {
        this.oauthApiSvc = oauthApiSvc;
    }

    @Override
    public OAuth2AccessToken generate(OAuth2TokenContext context) {
        if (!OAuth2TokenType.ACCESS_TOKEN.equals(context.getTokenType()) ||
                !OAuth2TokenFormat.REFERENCE.equals(context.getRegisteredClient().getTokenSettings().getAccessTokenFormat())) {
            return null;
        }
        String issuer = null;
        if (context.getAuthorizationServerContext() != null) {
            issuer = context.getAuthorizationServerContext().getIssuer();
        }
        RegisteredClient registeredClient = context.getRegisteredClient();

        Instant issuedAt = Instant.now();
        Instant expiresAt = issuedAt.plus(registeredClient.getTokenSettings().getAccessTokenTimeToLive());

        // @formatter:off
        OAuth2TokenClaimsSet.Builder claimsBuilder = OAuth2TokenClaimsSet.builder();
        if (StringUtils.hasText(issuer)) {
            claimsBuilder.issuer(issuer);
        }
        claimsBuilder
                .subject(context.getPrincipal().getName())
                .audience(Collections.singletonList(registeredClient.getClientId()))
                .issuedAt(issuedAt)
                .expiresAt(expiresAt)
                .notBefore(issuedAt)
                .id(UUID.randomUUID().toString());
        if (!CollectionUtils.isEmpty(context.getAuthorizedScopes())) {
            claimsBuilder.claim(OAuth2ParameterNames.SCOPE, context.getAuthorizedScopes());
        }
        OAuth2TokenClaimsSet accessTokenClaimsSet = claimsBuilder.build();
        Authentication principal = context.getPrincipal();
        UinoUserDetails user = (UinoUserDetails)principal.getPrincipal();
        String userStr ;
        userStr = user.getUserInfo().getId() + "&" + user.getUserInfo().getLoginCode() + "&"
                + user.getUserInfo().getDomainId();
        userStr = UUID.randomUUID()+SysUtil.StringUtil.Base64.encry(userStr);

        OAuth2AccessToken oAuth2AccessTokenClaims = new OAuth2AccessTokenClaims(OAuth2AccessToken.TokenType.BEARER,
                userStr, accessTokenClaimsSet.getIssuedAt(), accessTokenClaimsSet.getExpiresAt(),
                context.getAuthorizedScopes(), accessTokenClaimsSet.getClaims());
        // 保存token信息
        saveToken(oAuth2AccessTokenClaims, context);
        return oAuth2AccessTokenClaims;
    }

    private void saveToken(OAuth2AccessToken accessToken, OAuth2TokenContext context) {
        try {
            // 创建OauthTokenDetail对象
            OauthTokenDetail tokenDetail = new OauthTokenDetail();
            tokenDetail.setTokenCode(accessToken.getTokenValue());
            tokenDetail.setClient(context.getRegisteredClient().getClientId());

            Authentication principal = context.getPrincipal();
            if (principal.getPrincipal() instanceof UinoUserDetails) {
                UinoUserDetails user = (UinoUserDetails) principal.getPrincipal();
                tokenDetail.setUserName(user.getUsername());
                tokenDetail.setDomainId(user.getUserInfo().getDomainId());
            } else {
                tokenDetail.setUserName(principal.getName());
            }

            // 序列化认证信息
            try (ByteArrayOutputStream bos = new ByteArrayOutputStream();
                 ObjectOutputStream oos = new ObjectOutputStream(bos)) {
                oos.writeObject(principal);
                tokenDetail.setAuthentication(bos.toByteArray());
            }

            // 设置过期时间
            if (accessToken.getExpiresAt() != null) {
                tokenDetail.setExpirationTime(accessToken.getExpiresAt().toEpochMilli());
            }

            // 保存到数据库
            log.info("保存token信息: {}", tokenDetail.getTokenCode());
            oauthApiSvc.persistenceToken(tokenDetail);
        } catch (Exception e) {
            log.error("保存token信息失败", e);
        }
    }

    private static final class OAuth2AccessTokenClaims extends OAuth2AccessToken implements ClaimAccessor {
        private final Map<String, Object> claims;

        private OAuth2AccessTokenClaims(TokenType tokenType, String tokenValue,
                                        Instant issuedAt, Instant expiresAt, Set<String> scopes, Map<String, Object> claims) {
            super(tokenType, tokenValue, issuedAt, expiresAt, scopes);
            this.claims = claims;
        }

        @Override
        public Map<String, Object> getClaims() {
            return this.claims;
        }

    }
}

package com.uino.oauth.server.init;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.api.client.permission.IUserApiSvc;
import com.uino.oauth.server.init.http.ParameterRequestWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 验证码拦截器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ValidCodeFilter implements Filter {

    // url异常处理handler/与登陆验证失败保持一致跳回登陆
    @Autowired
    private AuthenticationFailureHandler authenticationFailureHandler;

    @Autowired
    private IUserApiSvc userApi;

    /**
     * 验证码错误异常
     *
     * <AUTHOR>
     */
    public class ValidCodeException extends AuthenticationException {

        public ValidCodeException(String msg) {
            super(msg);
        }

        private static final long serialVersionUID = 1L;
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
        String uri = httpServletRequest.getRequestURI();
        if (uri.contains("login") && httpServletRequest.getMethod().equals("POST")) {
            // cookie中有验证码就验证
            String validCode = request.getParameter("validCode");
            String realCode = null;
            try {
                for (Cookie cookie : httpServletRequest.getCookies()) {
                    if (cookie.getName().equals("validCode")) {
                        realCode = cookie.getValue();
                        break;
                    }
                }
            } catch (Exception e) {
            }
            if (StringUtils.isNotBlank(realCode)) {
                if (realCode == null) {
                    authenticationFailureHandler.onAuthenticationFailure(httpServletRequest,
                            (HttpServletResponse) response, new ValidCodeException("验证码验证异常请刷新后重试"));
                    return;
                }
                if (validCode == null || !realCode.equals(validCode)) {
                    authenticationFailureHandler.onAuthenticationFailure(httpServletRequest,
                            (HttpServletResponse) response, new ValidCodeException("验证码输入错误"));
                    return;
                }
            }
            //拼接域和名称,方便oauth框架内查询用户
            String username = request.getParameter("username");
            String domainId = request.getParameter("domainId");
            if (!"superadmin".equals(username) && domainId != null) {
                Map<String, Object> parameterMap = new HashMap<>();
                parameterMap.put("username", domainId + "+" + username);
                chain.doFilter(new ParameterRequestWrapper(httpServletRequest, parameterMap), httpServletResponse);
                return;
            }
        }
        chain.doFilter(httpServletRequest, httpServletResponse);

    }


}

package com.uino.oauth.common.service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.regex.Pattern;

import jakarta.annotation.PostConstruct;
import javax.naming.ldap.LdapContext;

import com.binary.jdbc.Page;
import com.uino.api.client.permission.IRoleApiSvc;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.api.client.sys.ILoginAuthConfigApiSvc;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.query.CSysUser;
import com.uino.bean.sys.base.LdapUserMapping;
import com.uino.bean.sys.base.LoginAuthConfig;
import com.uino.bean.sys.base.LoginLdapAuthConfig;
import com.uino.bean.sys.business.LoginAuthConfigDto;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uino.util.sys.LdapUtil;
import com.uino.util.sys.SysUtil;
import com.uino.util.sys.SysUtil.StringUtil;

import com.uino.oauth.common.bean.UinoUserDetails;
import com.uino.oauth.common.exception.UserFormatException;


import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class UinoLdapAuthenticationProvider {

    /** 刷新缓存的间隔时间 单位毫秒 */
    private static final int REFRESH_CACHE_INTERVAL_TIME = 30000;

    @Autowired
    ILoginAuthConfigApiSvc apiSvc;

    @Autowired
    IUserApiSvc userApiSvc;

    @Autowired
    IRoleApiSvc roleApiSvc;

    /** Ldap验证超时时间,单位秒 */
    @Value("${sys.integration.ldap.timeout:10}")
    public Integer timeout = 10;

    /** 是否自动创建用户 */
    @Value("${sys.integration.autoCreateUser:true}")
    public boolean autoCreateUser = true;

    /** 是否自动更新用户 */
    @Value("${sys.integration.autoUpdateUser:true}")
    public boolean autoUpdateUser = true;

    /** 30秒从数据库中刷新一次配置,然后刷新缓存 */
    private long lastRefreshTime = 0L;

    /** 记录配置最后一次修改时间最大值,用于减少刷新缓存 */
    private long configMaxModifyTime = 0L;

    /** 使用ldap验证的顺序, 这个值应该是更加创建时间排序的值 */
    private List<Long> sortedLdapIds = new ArrayList<Long>(20);

    /** ldap是否能够连接成功的缓存,{@link #REFRESH_CACHE_INTERVAL_TIME} */
    private Map<Long, Boolean> connStatusCache = new HashMap<Long, Boolean>();

    /*** ldap 连接默认指向用户的缓存 30秒一刷新 */
    @SuppressWarnings("unused")
    @Deprecated
    private Map<Long, Set<SysRole>> ldapIdCacheRole = new HashMap<Long, Set<SysRole>>();

    /** 刷新缓存同步锁 */
    private Object syncObj = new Object();

    private volatile boolean refreshConfging = false;

    @PostConstruct
    public void init() {
        log.info("init  ldap user details manager !");
        // refresh(true);
    }

    /**
     * 刷新ldap和对应的一些缓存信息
     * 
     * @param compel
     */
    protected void refresh(boolean compel) {
        if (refreshConfging) { return; }
        if (compel || System.currentTimeMillis() - lastRefreshTime > REFRESH_CACHE_INTERVAL_TIME) {
            if (refreshConfging) { return; }
            refreshConfging = true;
            try {
                synchronized (syncObj) {
                    sortedLdapIds.clear();// 清理本地缓存
                    connStatusCache.clear();//
                    // 查询全部现存并已经激活的配置项
                    LoginAuthConfigDto authConfigCdt = LoginAuthConfigDto.builder().protoStatus(1).pageNum(1)
                            .pageSize(1000).build();
                    Page<LoginAuthConfig> queryPage = apiSvc.queryPage(authConfigCdt);
                    List<LoginAuthConfig> data = queryPage.getData();
                    data = data == null ? new ArrayList<LoginAuthConfig>() : data;
                    sortLoginAuthConfig(data);
                    // 更加配置最后修改时间刷新LdapUtil相关内容
                    // 并移除掉不存在的配置项.
                    Set<Long> retainCacheIds = new HashSet<Long>();// 需要保留配置项ID,用于删掉不需要保留的数据
                    long tMaxModifyTime = 0L; // 用于刷新最后修改的记录
                    for (LoginAuthConfig authConfig : data) {
                        sortedLdapIds.add(authConfig.getId());
                        retainCacheIds.add(authConfig.getId());
                        if (authConfig.getLastModifyTime() > configMaxModifyTime) {
                            // 记录最大的修改时间
                            tMaxModifyTime = authConfig.getLastModifyTime() > tMaxModifyTime
                                    ? authConfig.getLastModifyTime() : tMaxModifyTime;
                            try {
                                putToLdapUtilContainer(authConfig);
                            } catch (Exception e) {
                                connStatusCache.put(authConfig.getId(), false);// 标记错误的ldap,加速登录
                                log.error("登录集成配置项[" + authConfig.getProtoName() + "],ID[ " + authConfig.getId() + "]异常"
                                        + e.getMessage());
                            }
                        }
                    }
                    LdapUtil.retainCahceByIdsRemoveOther(retainCacheIds);
                    // 用户与角色关系映射是直接从ldap中取的所以这个禁用了.
                    // refreshRoleCache(data);
                    if (tMaxModifyTime > configMaxModifyTime) {
                        configMaxModifyTime = tMaxModifyTime;
                    }
                    lastRefreshTime = System.currentTimeMillis();
                }
            } finally {
                refreshConfging = false;
            }
        }
    }

    @SuppressWarnings("unused")
    @Deprecated
    private void refreshRoleCache(List<LoginAuthConfig> data) {
        ldapIdCacheRole.clear();
        Set<String> roleNameSet = new HashSet<String>();
        Map<Long, Set<String>> ldapRoles = new HashMap<Long, Set<String>>();
        for (LoginAuthConfig authConfig : data) {
            String rolesStr = authConfig.getLdapUserMapping().getRoles();
            if (StringUtil.isNotBack(rolesStr)) {
                String[] split = rolesStr.split(",");
                Set<String> lr = new HashSet<String>();
                for (String string : split) {
                    if (StringUtil.isNotBack(string)) {
                        roleNameSet.add(string);
                        lr.add(string);
                    }
                }
                if (lr.size() > 0) {
                    ldapRoles.put(authConfig.getId(), lr);
                }
            }
        }
        // 刷新角色缓存
        if (roleNameSet.size() > 0) {
            BoolQueryBuilder must = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termsQuery("roleName.keyword", roleNameSet));
            List<SysRole> roles = roleApiSvc.getRolesByQuery(must);
            Map<String, SysRole> nameMap = new HashMap<String, SysRole>();
            for (SysRole sysRole : roles) {
                nameMap.put(sysRole.getRoleName(), sysRole);
            }
            for (Entry<Long, Set<String>> entry : ldapRoles.entrySet()) {
                Set<SysRole> ldapSysRoles = new HashSet<SysRole>();
                Long ldapId = entry.getKey();
                for (String rName : entry.getValue()) {
                    SysRole sysRole = nameMap.get(rName);
                    if (sysRole != null) {
                        ldapSysRoles.add(sysRole);
                    }
                }
                ldapIdCacheRole.put(ldapId, ldapSysRoles);
            }
        }
    }

    /**
     * 需要要求先创建的先验证. <AUTHOR> 2020-04-24 14:43:00
     * 
     * @param data
     */
    protected void sortLoginAuthConfig(List<LoginAuthConfig> data) {
        if (data.isEmpty()) { return; }
        java.util.Collections.sort(data, new Comparator<LoginAuthConfig>() {

            @Override
            public int compare(LoginAuthConfig o1, LoginAuthConfig o2) {
                return (int) (o1.getCreateTime() - o2.getCreateTime());
            }
        });
    }

    public void clear() {
        synchronized (syncObj) {
            LdapUtil.clearCache();
        }
    }

    /**
     * 将配置存放到缓存中.
     * 
     * @param authConfig
     */
    private void putToLdapUtilContainer(LoginAuthConfig authConfig) {
        LoginLdapAuthConfig prop = authConfig.getLdapAuthConfig();
        MessageUtil.checkEmpty(prop, "connect Ldap Properties");
        String hostName = prop.getHostName();
        String port = prop.getPort();
        MessageUtil.checkEmpty(hostName, "connect Ldap Properties 'url'");
        MessageUtil.checkEmpty(port, "connect Ldap Properties 'url'");
        String baseDn = prop.getBaseDn();
        String userNameAttr = prop.getUserNameAttr();
        LdapUserMapping defs = authConfig.getLdapUserMapping();
        Map<String, String> attrMaps = new HashMap<String, String>();
        Map<String, Object> innerMap = ((JSONObject) JSON.toJSON(defs)).getInnerMap();
        Set<Entry<String, Object>> entrySet = innerMap.entrySet();
        for (Entry<String, Object> entry : entrySet) {
            attrMaps.put(entry.getKey(), String.valueOf(entry.getValue()));
        }
        LdapUtil ldapUtil = LdapUtil.putLdapUtil(authConfig.getId(), hostName, port, baseDn, userNameAttr, attrMaps,
                prop.getLoginUserDn(), prop.getPassword(), timeout * 1000);
        if (!ldapUtil.hasBaseUserAndPwd()) {
            // ldap模式
            String userNameRdnAttr = prop.getUserNameRdnAttr();
            MessageUtil.checkEmpty(userNameRdnAttr, "connect Ldap Properties 'userIdAttr'");
            ldapUtil.setSeachAttr(userNameRdnAttr);
        } else {
            // 用户名模式
            int i1 = prop.getLoginUserDn().indexOf(",");
            baseDn = prop.getLoginUserDn().substring(i1 + 1, prop.getLoginUserDn().length());
            ldapUtil.setDomain(baseDn);
        }
    }

    public boolean isActive() {
        refresh(false);
        return !sortedLdapIds.isEmpty();
    }

    public boolean authenticate(UserDetailsService userDetailsService, UserDetails userDetails,
            Authentication authentication) throws AuthenticationException {
        UinoUserDetails ud = userDetails == null ? null : (UinoUserDetails) userDetails;
        if (userDetails == null && !autoCreateUser) {
            if (log.isDebugEnabled()) {
                log.debug("用户[" + authentication.getName() + "]不存在,且未开启自动注入用户功能登录失败!");
            }
            return false;
        }
        // UinoUserDetailsManager uds = (UinoUserDetailsManager)
        // userDetailsService;
        String name = authentication.getName();
        // String pwd = authentication.getCredentials().toString();
        String pwd = authentication.getCredentials().toString();
        pwd = SysUtil.EncryptDES.decryptDES(pwd);
        // TODO pwd当前是明文,如果修改后需要解密
        for (Long ldapId : sortedLdapIds) {
            LdapUtil ldapUtil = LdapUtil.getLdapUtil(ldapId);
            if (ldapUtil != null) {
                LdapContext ldapContext = validate(ldapUtil, name, pwd);
                try {
                    if (ldapContext != null) {
                        try {
                            if (ud == null && autoCreateUser) {
                                createUserByLdap(authentication, ldapUtil, ldapContext);
                            } else if (autoUpdateUser) {
                                updateUserByLdap(authentication, ldapUtil, ldapContext);
                            }
                        } catch (Exception e) {
                            throw new UserFormatException(e.getMessage(), e.getCause());
                        }
                        return true;
                    }
                } finally {
                    LdapUtil.closeLdapCtx(ldapContext);
                }
            }
        }
        return false;
    }

    /**
     * 验证用户名或密码
     * 
     * @param ldapUtil
     * @param username
     * @param passwd
     * @return null验证失败,否则验证通过
     */
    private LdapContext validate(LdapUtil ldapUtil, String username, String passwd) {
        String userDn = null;
        LdapContext ldapCtx = null;
        try {
            if (ldapUtil.hasBaseUserAndPwd()) {
                ldapCtx = ldapUtil.getLdapContext();
                String searchDN = ldapUtil.serachDN(ldapCtx, username);
                userDn = searchDN;
            } else {
                String rdn = ldapUtil.getSeachAttr();
                String baseDn = ldapUtil.getDomain();
                userDn = rdn + "=" + username + "," + baseDn;
            }
        } catch (Exception e) {
            log.error("validate ldap context error:", e);
        } finally {
            LdapUtil.closeLdapCtx(ldapCtx);
        }
        try {
            ldapCtx = ldapUtil.getLdapContext(userDn, passwd);
        } catch (Exception e) {
            return null;
        }
        return ldapCtx;
    }

    private void createUserByLdap(Authentication authentication, LdapUtil ldapUtil, LdapContext ldapContext) {
        Map<String, String> attrMaps = ldapUtil.getAttrMaps();
        Set<String> ldapKeys = new HashSet<String>(attrMaps.values());
        Map<String, String> serachInfo = ldapUtil.serachInfo(ldapContext, authentication.getName(), ldapKeys);
        UserInfo sysUser = new UserInfo();
        String userName = serachInfo.get(attrMaps.get("userName"));
        String emailAdress = serachInfo.get(attrMaps.get("emailAdress"));
        String mobileNo = serachInfo.get(attrMaps.get("mobileNo"));
        String notes = serachInfo.get(attrMaps.get("notes"));
        String role = serachInfo.get(attrMaps.get("roles"));
        sysUser.setLoginCode(authentication.getName());
        sysUser.setUserName(fixUserName(userName, authentication.getName()));
        sysUser.setMobileNo(fixMobileNo(mobileNo, null));
        sysUser.setEmailAdress(fixMail(emailAdress, null));
        sysUser.setLoginPasswd(randPwd(sysUser.getLoginCode()));
        sysUser.setNotes(notes);
        sysUser.setCreator("LDAP_AUTO_CREATE");
        // Set<SysRole> sysRoles = ldapIdCacheRole.get(ldapUtil.getId());

        // EA测试环境LDAP角色默认设置为“LDAP用户”
        role = "LDAP用户";
        if (!BinaryUtils.isEmpty(role)) {
            BoolQueryBuilder must = QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("roleName.keyword", role));
            List<SysRole> sysRoles = roleApiSvc.getRolesByQuery(must);
            if (sysRoles != null && sysRoles.size() > 0) {
                sysUser.setRoles(new HashSet<SysRole>(sysRoles));
            }
        }
        // 保存用户? 保存用户和组织的关系,保存用户和角色的关系.
        userApiSvc.saveOrUpdate(sysUser);
    }

    private void updateUserByLdap(Authentication authentication, LdapUtil ldapUtil, LdapContext ldapContext) {
        Map<String, String> attrMaps = ldapUtil.getAttrMaps();
        Set<String> ldapKeys = new HashSet<String>(attrMaps.values());
        Map<String, String> serachInfo = ldapUtil.serachInfo(ldapContext, authentication.getName(), ldapKeys);
        String userName = serachInfo.get(attrMaps.get("userName"));
        String emailAdress = serachInfo.get(attrMaps.get("emailAdress"));
        String mobileNo = serachInfo.get(attrMaps.get("mobileNo"));
        CSysUser cdt = new CSysUser();
        cdt.setLoginCodeEqual(authentication.getName());
        List<UserInfo> sysUsers = userApiSvc.getUserInfoByCdt(cdt, false);
        if (sysUsers.isEmpty()) { return; }
        UserInfo sysUser = sysUsers.get(0);
        sysUser.setUserName(fixUserName(userName, sysUser.getUserName()));
        sysUser.setMobileNo(fixMobileNo(mobileNo, sysUser.getMobileNo()));
        sysUser.setEmailAdress(fixMail(emailAdress, sysUser.getEmailAdress()));
        // 保存用户? 保存用户和组织的关系,保存用户和角色的关系.
        userApiSvc.saveOrUpdate(sysUser);
    }

    final String DEF_USERNAME = "autoCreate";

    final String DEF_MAIL = "<EMAIL>";

    final String DEF_MOBILE_NO = "8888-888888";

    final String regexEmail = "\\w[-\\w.+]*@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";

    final String regenMobile = "[0-9\\-\\(\\)]+$";

    private String fixMail(String newMail, String oldMail) {
        if (vMail(newMail)) {
            return newMail;
        } else {
            if (vMail(oldMail)) {
                return oldMail;
            } else {
                return DEF_MAIL;
            }
        }
    }

    private String fixUserName(String newName, String oldName) {
        if (vUserName(newName)) {
            return newName;
        } else {
            if (vUserName(oldName)) {
                return oldName;
            } else {
                return DEF_USERNAME;
            }
        }
    }

    private String fixMobileNo(String newMobileNo, String oldMobileNo) {
        if (vMobileNo(newMobileNo)) {
            return newMobileNo;
        } else {
            if (vMobileNo(oldMobileNo)) {
                return oldMobileNo;
            } else {
                return DEF_MOBILE_NO;
            }
        }
    }

    private boolean vMobileNo(String mobileNo) {
        if (StringUtil.isBack(mobileNo) || mobileNo.length() > 50) { return false; }
        return Pattern.matches(regenMobile, mobileNo);
    }

    private boolean vUserName(String username) {
        if (StringUtil.isBack(username) || username.length() > 50) { return false; }
        return true;
    }

    private boolean vMail(String mail) {
        if (StringUtil.isBack(mail) || mail.length() > 50) { return false; }
        return Pattern.matches(regexEmail, mail);
    }

    private String randPwd(String loginCode) {
        String a = "ab";
        String b = "cd";
        if (loginCode.length() > 4) {
            a = loginCode.substring(0, 2);
            b = loginCode.substring(2, 4);
        }
        return a + "T_w" + System.currentTimeMillis() % 100 + "er" + b + System.currentTimeMillis() % 1000;
    }
    //
    // @Override
    // public boolean supports(Class<?> authentication) {
    // return true;
    // }
}

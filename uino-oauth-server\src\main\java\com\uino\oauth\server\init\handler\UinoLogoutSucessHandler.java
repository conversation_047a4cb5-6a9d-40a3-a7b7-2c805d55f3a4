package com.uino.oauth.server.init.handler;

import java.io.IOException;

import jakarta.annotation.PostConstruct;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.web.authentication.AbstractAuthenticationTargetUrlRequestHandler;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.stereotype.Component;

import com.uino.oauth.common.service.UinoTokenExtractor;
import com.uino.oauth.common.service.UinoTokenStore;

@Component
public class UinoLogoutSucessHandler extends AbstractAuthenticationTargetUrlRequestHandler
		implements LogoutSuccessHandler {
	@Value("${logout.sucess.url}")
	private String defaultLogoutSucessUrl;

	@Autowired
	private UinoTokenStore tokenSvc;

	@Autowired
	private UinoTokenExtractor tokenExtractor;

	@PostConstruct
	public void init() {
		super.setTargetUrlParameter("goUrl");
		super.setDefaultTargetUrl(defaultLogoutSucessUrl);
	}

	@Override
	public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication)
			throws IOException, ServletException {
		// 从请求中提取令牌
		String tokenValue = tokenExtractor.extractToken(request);
		if (tokenValue != null) {
			// 直接移除令牌
			tokenSvc.removeAccessToken(tokenValue);
		} else if (authentication != null) {
			// 尝试从认证对象获取令牌
			Jwt token = tokenSvc.getAccessToken(authentication);
			if (token != null) {
				tokenSvc.removeAccessToken(token);
			}
		}
		super.handle(request, response, authentication);
	}
}

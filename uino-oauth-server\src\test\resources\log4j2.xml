<?xml version="1.0" encoding="UTF-8"?>
<!--    Log level and priority order: OFF> FATAL> ERROR> WARN> INFO> DEBUG> TRACE> ALL -->
<configuration monitorInterval="30">
    <Properties>
        <property name="LOG_PATTERN" value="%date{HH:mm:ss} %-5level [%thread] %l - %msg%n"/>
        <property name="LOG_PATTERN_FILE" value="%date{yyyy-MM-dd HH:mm:ss} %-5level %l - %msg%n"/>
        <property name="FILE_PATH" value="./logs/"/>
        <property name="FILE_NAME" value="oauth"/>
    </Properties>

    <appenders>
        <!--    console config-->
        <console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <ThresholdFilter level="debug" onMatch="ACCEPT" onMismatch="DENY"/>
        </console>

        <!--    info config-->
        <RollingFile name="RollingFileInfo" fileName="${FILE_PATH}/info.log" filePattern="${FILE_PATH}/info_old_%i.log">
            <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN_FILE}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="6"/>
        </RollingFile>

        <!--    debug config-->
        <RollingFile name="RollingFileDebug" fileName="${FILE_PATH}/debug.log" filePattern="${FILE_PATH}/debug_old_%i.log">
            <ThresholdFilter level="debug" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN_FILE}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
    </appenders>

    <loggers>
        <!--    filter invalid dependent package logs-->
        <Logger name="com.uino.dao.util" level="DEBUG" additivity="false">
            <AppenderRef ref="RollingFileDebug"/>
        </Logger>
        <logger name="org.springframework" level="WARN"/>
        <logger name="springfox" level="WARN"/>
        <logger name="com.ulisesbocchio" level="WARN"/>
        <logger name="org.hibernate" level="WARN"/>
        <logger name="com.alibaba.nacos" level="WARN"/>
        <logger name="com.alibaba.cloud" level="ERROR"/>
        <logger name="org.apache.http" level="INFO"/>
        <logger name="org.elasticsearch" level="INFO"/>
        <logger name="io.netty" level="INFO"/>
        <logger name="org.redisson" level="WARN"/>
        <logger name="com.netflix" level="ERROR"/>
        <logger name="okhttp3" level="WARN"/>
        <!--    root config-->
        <root level="info">
            <appender-ref ref="Console"/>
            <appender-ref ref="RollingFileInfo"/>
<!--            <appender-ref ref="RollingFileDebug"/>-->
        </root>
    </loggers>
</configuration>
package com.uino.oauth.server.init;

import java.util.Iterator;
import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Value("#{'${uino.eam.allowed.origins:*}'.split(',')}")
    private List<String> allowOrigins;

    // 解决跨域问题
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        Iterator<String> iterator = allowOrigins.iterator();
        while (iterator.hasNext()){
            String next = iterator.next();
            if(next == null || "".equals(next.trim())){
                iterator.remove();
            }
        }
        registry.addMapping("/**").allowedOriginPatterns("*")
                .allowedMethods("POST", "GET", "PUT", "OPTIONS", "DELETE").maxAge(3600).allowCredentials(true);
    }
}

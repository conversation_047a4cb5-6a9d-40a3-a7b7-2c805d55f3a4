package com.uino.oauth.common.service.dao;

import java.util.List;
import java.util.stream.Collectors;

import jakarta.annotation.PostConstruct;

import com.uino.dao.AbstractESBaseDao;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Repository;

import com.alibaba.fastjson.JSONObject;
import com.uino.oauth.common.bean.UinoSessionCache;

import lombok.extern.slf4j.Slf4j;

@Repository
@Slf4j
public class ESUinoSessionCacheSvc extends AbstractESBaseDao<UinoSessionCache, JSONObject> {

    @Override
    public String getIndex() {
        // TODO Auto-generated method stub
        return "uino_session_cache";
    }

    @Override
    public String getType() {
        // TODO Auto-generated method stub
        return "uino_session_cache";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

    /**
     * 根据sessionid获取sessionCache
     * 
     * @param sessionId
     * @return
     */
    public UinoSessionCache getBySessionId(String sessionId) {
        List<UinoSessionCache> res = this.getListByQuery(QueryBuilders.termQuery("sessionId.keyword", sessionId));
        if (res != null && res.size() > 1) {
            log.error("发现重复session，直接删除所有该session的请求缓存进行容错处理");
            this.deleteByIds(res.stream().collect(Collectors.groupingBy(UinoSessionCache::getId)).keySet());
            return null;
        }
        return res != null && res.size() > 0 ? res.get(0) : null;
    }
}

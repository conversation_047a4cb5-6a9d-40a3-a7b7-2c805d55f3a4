package com.uino.oauth.common.service;

import com.uino.api.client.permission.IOauthApiSvc;
import com.uino.bean.permission.base.OAuthClientDetail;
import com.uino.bean.permission.business.request.RegisterClientReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.security.oauth2.server.authorization.settings.ClientSettings;
import org.springframework.security.oauth2.server.authorization.settings.OAuth2TokenFormat;
import org.springframework.security.oauth2.server.authorization.settings.TokenSettings;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 新版客户端详情服务，基于 RegisteredClientRepository
 */
@Service
public class UinoRegisteredClientRepository implements RegisteredClientRepository {

    @Autowired
    private IOauthApiSvc clientSvc;

    @Override
    public void save(RegisteredClient registeredClient) {
        // 将 RegisteredClient 转换为 RegisterClientReq 并保存
        RegisterClientReq saveDto = convertToRegisterClientReq(registeredClient);
        clientSvc.register(saveDto);
    }

    @Override
    public RegisteredClient findById(String id) {
        // 通过 ID 查询客户端信息
        OAuthClientDetail detailInfo = clientSvc.getClientInfoByCode(id);
        if (detailInfo == null) {
            return null;
        }
        return convertToRegisteredClient(detailInfo);
    }

    @Override
    public RegisteredClient findByClientId(String clientId) {
        // 通过 clientId 查询客户端信息
        OAuthClientDetail detailInfo = clientSvc.getClientInfoByCode(clientId);
        if (detailInfo == null) {
            return null;
        }
        return convertToRegisteredClient(detailInfo);
    }

    /**
     * 获取所有客户端列表
     */
    public List<RegisteredClient> findAll() {
        return clientSvc.getAllClents().stream()
                .map(this::convertToRegisteredClient)
                .collect(Collectors.toList());
    }

    /**
     * 更新客户端密钥
     */
    public void updateClientSecret(String clientId, String secret) {
        RegisteredClient client = findByClientId(clientId);
        if (client == null) {
            throw new IllegalArgumentException("客户端未注册");
        }
        // 重新构建 RegisteredClient 并更新密钥
        RegisteredClient updatedClient = RegisteredClient.from(client)
                .clientSecret(secret)
                .build();
        save(updatedClient);
    }

    /**
     * 删除客户端
     */
    public void removeClientDetails(String clientId) {
        clientSvc.removeClientInfoByCode(clientId);
    }

    /**
     * 将 RegisteredClient 转换为 RegisterClientReq
     */
    private RegisterClientReq convertToRegisterClientReq(RegisteredClient registeredClient) {
        RegisterClientReq req = new RegisterClientReq();

        // 设置基本信息
        req.setClientCode(registeredClient.getClientId());
        req.setClientSecret(registeredClient.getClientSecret());

        // 设置授权类型
        Set<String> grantTypes = new HashSet<>();
        registeredClient.getAuthorizationGrantTypes().forEach(grantType -> {
            grantTypes.add(grantType.getValue());
        });
        req.setAuthorizedGrantTypes(grantTypes);

        // 设置重定向URI
        Set<String> redirectUris = new HashSet<>(registeredClient.getRedirectUris());
        req.setCodeToTokenUrl(redirectUris);

        // 设置作用域
        Set<String> scopes = new HashSet<>(registeredClient.getScopes());
        req.setScopes(scopes);

        // 设置令牌有效期
        TokenSettings tokenSettings = registeredClient.getTokenSettings();
        if (tokenSettings != null) {
            if (tokenSettings.getAccessTokenTimeToLive() != null) {
                req.setAccessTokenValiditySeconds(tokenSettings.getAccessTokenTimeToLive().getSeconds());
            }
            if (tokenSettings.getRefreshTokenTimeToLive() != null) {
                req.setRefreshTokenValiditySeconds(tokenSettings.getRefreshTokenTimeToLive().getSeconds());
            }
        }

        // 设置客户端设置
        ClientSettings clientSettings = registeredClient.getClientSettings();
        if (clientSettings != null) {
            req.setAutoApprove(!clientSettings.isRequireAuthorizationConsent());
        }

        // 设置客户端认证方法
        req.setSecretRequired(true); // 默认需要密钥

        return req;
    }

    /**
     * 将 OAuthClientDetail 转换为 RegisteredClient
     */
    private RegisteredClient convertToRegisteredClient(OAuthClientDetail detailInfo) {
        RegisteredClient.Builder builder = RegisteredClient.withId(detailInfo.getId().toString())
                .clientId(detailInfo.getClientCode())
                .clientSecret(detailInfo.getClientSecret())
                .clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_POST);

        // 设置授权类型
        Set<String> grantTypes = detailInfo.getAuthorizedGrantTypes();
        if (grantTypes != null) {
            if (grantTypes.contains("authorization_code")) {
                builder.authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE);
            }
            if (grantTypes.contains("refresh_token")) {
                builder.authorizationGrantType(AuthorizationGrantType.REFRESH_TOKEN);
            }
            if (grantTypes.contains("client_credentials")) {
                builder.authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS);
            }
            // 注意：PASSWORD授权类型在OAuth 2.1中已被弃用，但为了兼容性仍然支持
            if (grantTypes.contains("password")) {
                // 使用自定义授权类型替代已弃用的PASSWORD
                builder.authorizationGrantType(new AuthorizationGrantType("password"));
            }
        }

        // 设置重定向URI
        Set<String> redirectUris = detailInfo.getCodeToTokenUrl();
        if (redirectUris != null) {
            for (String uri : redirectUris) {
                builder.redirectUri(uri);
            }
        }

        // 设置作用域
        Set<String> scopes = detailInfo.getScopes();
        if (scopes != null) {
            for (String scope : scopes) {
                builder.scope(scope);
            }
        }

        // 设置客户端设置
        ClientSettings clientSettings = ClientSettings.builder()
                .requireAuthorizationConsent(detailInfo.isAutoApprove() ? false : true)
                .build();
        builder.clientSettings(clientSettings);

        // 设置令牌设置
        TokenSettings tokenSettings = TokenSettings.builder()
                .accessTokenTimeToLive(Duration.ofSeconds(detailInfo.getAccessTokenValiditySeconds()))
                .refreshTokenTimeToLive(Duration.ofSeconds(detailInfo.getRefreshTokenValiditySeconds()))
                .accessTokenFormat(OAuth2TokenFormat.REFERENCE)
                .build();
        builder.tokenSettings(tokenSettings);

        return builder.build();
    }
}

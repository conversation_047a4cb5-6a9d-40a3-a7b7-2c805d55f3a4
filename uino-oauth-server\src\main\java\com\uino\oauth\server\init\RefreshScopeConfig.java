package com.uino.oauth.server.init;

import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.SimpleThreadScope;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025/4/27 18:43
 */
@Configuration
public class RefreshScopeConfig {

    @Bean
    public static BeanFactoryPostProcessor beanFactoryPostProcessor() {
        return beanFactory -> {
            beanFactory.registerScope("refresh", new SimpleThreadScope());
        };
    }
}
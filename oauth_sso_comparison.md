# OAuth 2.0 与 SSO (Single Sign-On) 的详细对比

## 核心概念

*   **OAuth 2.0 (Open Authorization 2.0):**
    *   **定义:** 一个开放的 **授权** 框架。 <mcreference link="https://workos.com/blog/sso-vs-oauth" index="1">1</mcreference> <mcreference link="https://www.pomerium.com/blog/sso-oauth2-vs-oidc-vs-saml" index="2">2</mcreference>
    *   **目的:** 允许第三方应用程序在用户的明确授权下，有限度地访问用户在某个服务提供商（如Google, Facebook, GitHub等）上的受保护资源，而无需将用户的原始凭证（如用户名和密码）暴露给第三方应用。
    *   **关注点:** 如何安全地委托权限，即“你能做什么”。
    *   **例子:** 你授权一个照片打印服务访问你存储在Google Photos上的照片，但该服务并不知道你的Google密码。

*   **SSO (Single Sign-On):**
    *   **定义:** 一种 **认证** 机制或用户体验目标。 <mcreference link="https://workos.com/blog/sso-vs-oauth" index="1">1</mcreference> <mcreference link="https://systemdesignschool.io/blog/sso-vs-oauth" index="5">5</mcreference>
    *   **目的:** 用户只需进行一次身份验证（登录），之后就可以无缝访问多个相互独立的、但信任同一身份提供者 (IdP) 的应用系统或服务，无需在每个应用中重复输入凭证。
    *   **关注点:** 简化用户登录流程，提升用户体验和管理效率，即“你是谁”。
    *   **例子:** 你登录了公司的企业门户，之后可以直接访问邮件系统、CRM系统、HR系统等，无需为每个系统单独登录。

## 主要区别点

| 特性         | OAuth 2.0                                     | SSO (Single Sign-On)                             |
|--------------|-----------------------------------------------|--------------------------------------------------|
| **主要功能** | **授权 (Authorization)** - 授予权限           | **认证 (Authentication)** - 验证身份             |
| **解决的问题** | 如何让第三方应用安全地访问用户资源            | 如何让用户一次登录访问多个应用                   |
| **交互方**   | 用户、客户端应用、授权服务器、资源服务器        | 用户、服务提供者 (SP)、身份提供者 (IdP)          |
| **凭证共享** | 不共享用户原始凭证，通过令牌 (Access Token) 授权 | 用户凭证由IdP管理，SP信任IdP的认证结果         |
| **核心产物** | 访问令牌 (Access Token)、刷新令牌 (Refresh Token) | 安全断言 (如SAML Assertion) 或身份令牌 (ID Token) |
| **典型场景** | 第三方应用集成、API访问控制                   | 企业内部应用统一登录、跨站点/应用的用户体验优化 |

## 工作流程简述

*   **OAuth 2.0 (以授权码流程为例):**

    ```mermaid
    sequenceDiagram
        participant UserAgent as User-Agent/Browser
        participant ClientApp as Client Application
        participant AuthServer as Authorization Server
        participant ResourceServer as Resource Server

        UserAgent->>ClientApp: 1. 用户访问客户端应用
        ClientApp->>UserAgent: 2. 重定向到授权服务器 (携带 client_id, redirect_uri, scope, state)
        UserAgent->>AuthServer: 3. 用户向授权服务器请求授权
        AuthServer-->>UserAgent: 4. (用户登录) & 授权确认页面
        UserAgent->>AuthServer: 5. 用户同意授权
        AuthServer->>UserAgent: 6. 重定向回客户端应用 (携带 authorization_code, state)
        UserAgent->>ClientApp: 7. 将授权码发送给客户端应用
        ClientApp->>AuthServer: 8. 客户端应用使用授权码请求访问令牌 (携带 grant_type=authorization_code, code, redirect_uri, client_id, client_secret)
        AuthServer->>ClientApp: 9. 授权服务器返回访问令牌 (access_token) 和可选的刷新令牌 (refresh_token)
        ClientApp->>ResourceServer: 10. 客户端应用使用访问令牌请求资源
        ResourceServer->>ClientApp: 11. 资源服务器返回受保护的资源
    end
    ```

    **文字描述:**
    1.  用户尝试通过客户端应用访问其在资源服务器上的资源。
    2.  客户端应用将用户重定向到授权服务器请求授权。
    3.  用户在授权服务器上登录并同意授权。
    4.  授权服务器向客户端应用发放一个授权码。
    5.  客户端应用使用授权码向授权服务器请求访问令牌。
    6.  授权服务器验证授权码，并发放访问令牌给客户端应用。
    7.  客户端应用使用访问令牌向资源服务器请求受保护的资源。
    8.  资源服务器验证访问令牌，并返回资源。

*   **SSO (以SAML为例):**

    ```mermaid
    sequenceDiagram
        participant UserAgent as User-Agent/Browser
        participant SP as Service Provider
        participant IdP as Identity Provider

        UserAgent->>SP: 1. 用户尝试访问服务提供者 (SP)
        SP->>UserAgent: 2. SP发现用户未认证，生成SAML AuthnRequest，重定向用户到IdP
        UserAgent->>IdP: 3. 用户浏览器向IdP发送SAML AuthnRequest
        IdP-->>UserAgent: 4. (如果用户未登录IdP) IdP提示用户登录
        UserAgent->>IdP: 5. 用户向IdP提供凭证
        IdP->>IdP: 6. IdP验证用户凭证
        IdP->>UserAgent: 7. IdP生成SAML Assertion，通过浏览器重定向回SP (POST绑定)
        UserAgent->>SP: 8. 用户浏览器将SAML Assertion提交给SP的ACS URL
        SP->>SP: 9. SP验证SAML Assertion (签名, 条件, 属性等)
        SP-->>UserAgent: 10. SP授予用户访问权限，用户成功登录SP
    end
    ```

    **文字描述:**
    1.  用户尝试访问一个服务提供者 (SP)。
    2.  SP发现用户未认证，将用户重定向到身份提供者 (IdP)。
    3.  用户在IdP处进行身份验证（如果尚未登录）。
    4.  IdP成功验证用户身份后，生成一个包含用户身份信息的SAML断言。
    5.  IdP将SAML断言发送回用户的浏览器，浏览器再将其提交给SP。
    6.  SP验证SAML断言的有效性和IdP的签名，如果通过，则认为用户已认证，并授予访问权限。

## OAuth 2.0 与 SSO 的关系

*   **OAuth 2.0 不是 SSO，但可以用于构建 SSO 解决方案。** <mcreference link="https://stackoverflow.com/questions/77679120/what-is-the-difference-between-sso-and-oauth" index="4">4</mcreference>
*   当 OAuth 2.0 与 **OpenID Connect (OIDC)** 结合使用时，可以实现认证功能，从而支持SSO。OIDC是在OAuth 2.0之上构建的一个身份层，它引入了ID Token的概念，专门用于传递用户的身份信息。 <mcreference link="https://www.pomerium.com/blog/sso-oauth2-vs-oidc-vs-saml" index="2">2</mcreference>
*   很多现代的SSO解决方案，特别是面向消费者或Web/移动应用的场景，会采用 OAuth 2.0 + OIDC 的组合。
*   传统的企业级SSO更多依赖SAML协议。 <mcreference link="https://workos.com/blog/sso-vs-oauth" index="1">1</mcreference>

## 总结

简单来说：

*   **OAuth 2.0** 是关于 **“允许应用X代表你去做事情Y”** (授权)。
*   **SSO** 是关于 **“你只需要登录一次，就可以访问所有相关的应用”** (认证)。

两者解决的问题不同，但 OAuth 2.0 (特别是结合OIDC时) 可以作为实现SSO的技术手段之一。选择哪种技术或组合取决于具体的应用场景和需求。
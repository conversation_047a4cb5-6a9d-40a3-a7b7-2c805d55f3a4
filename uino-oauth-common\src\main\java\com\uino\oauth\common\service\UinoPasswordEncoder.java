package com.uino.oauth.common.service;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.security.crypto.password.PasswordEncoder;

import com.binary.core.encrypt.Encrypt;

/**
 * 自定义加密
 * 
 * <AUTHOR>
 *
 */
public class UinoPasswordEncoder implements PasswordEncoder {

	@Override
	public String encode(CharSequence rawPassword) {
		String pwd = rawPassword.toString();
		return DigestUtils.sha256Hex(pwd);
	}

	@Override
	public boolean matches(CharSequence rawPassword, String encodedPassword) {
		// 兼容旧版本MD5加密
		if (encodedPassword.length() > 32) {
			return encode(rawPassword).equals(encodedPassword);
		} else {
			return Encrypt.encrypt(rawPassword.toString()).equals(encodedPassword);
		}
	}

}

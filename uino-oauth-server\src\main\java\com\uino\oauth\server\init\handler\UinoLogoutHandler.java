package com.uino.oauth.server.init.handler;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.web.authentication.logout.LogoutHandler;
import org.springframework.stereotype.Component;

import com.uino.oauth.common.service.UinoTokenExtractor;
import com.uino.oauth.common.service.UinoTokenStore;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class UinoLogoutHandler implements LogoutHandler {
	@Autowired
	private UinoTokenStore tokenStore;

	@Autowired
	private UinoTokenExtractor tokenExtractor;

	@Override
	public void logout(HttpServletRequest request, HttpServletResponse response, Authentication authentication) {
		try {
			String tokenCode = tokenExtractor.extractToken(request);
			if (tokenCode != null) {
				// 直接移除令牌
				tokenStore.removeAccessToken(tokenCode);
				log.info("token【{}】登出成功", tokenCode);
			} else if (authentication != null) {
				// 尝试从认证对象获取令牌
				Jwt token = tokenStore.getAccessToken(authentication);
				if (token != null) {
					tokenStore.removeAccessToken(token);
					log.info("token【{}】登出成功", token.getTokenValue());
				}
			}
		} catch (Exception e) {
			log.error("登出异常", e);
		}
	}
}

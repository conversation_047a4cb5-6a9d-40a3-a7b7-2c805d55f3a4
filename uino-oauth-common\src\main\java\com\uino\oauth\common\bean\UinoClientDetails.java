package com.uino.oauth.common.bean;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.uino.bean.permission.base.OAuthClientDetail;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.util.Assert;

/**
 * 客户端详情实体
 * 在Spring Security 6中不再实现ClientDetails接口
 *
 * <AUTHOR>
 */
public class UinoClientDetails {

	private static final long serialVersionUID = 1L;

	private OAuthClientDetail clientDetailInfo;

	public UinoClientDetails(OAuthClientDetail clientDetailInfo) {
		Assert.notNull(clientDetailInfo, "构建客户端不可为空");
		this.clientDetailInfo = clientDetailInfo;
	}

	/**
	 * 获取客户端信息真实对象
	 *
	 * @return 客户端详情信息
	 */
	public OAuthClientDetail getClientInfo() {
		return clientDetailInfo;
	}

	/**
	 * 获取客户端id
	 *
	 * @return 客户端ID
	 */
	public String getClientId() {
		return clientDetailInfo.getClientCode();
	}

	/**
	 * 获取该客户端可持有的资源
	 *
	 * @return 资源ID集合
	 */
	public Set<String> getResourceIds() {
		return clientDetailInfo.getResourceIds();
	}

	/**
	 * 是否验证该客户端密钥
	 *
	 * @return 是否需要密钥
	 */
	public boolean isSecretRequired() {
		return clientDetailInfo.isSecretRequired();
	}

	/**
	 * 获取该客户端密钥
	 *
	 * @return 客户端密钥
	 */
	public String getClientSecret() {
		return clientDetailInfo.getClientSecret();
	}

	/**
	 * 是否限定该客户端权限范围
	 *
	 * @return 是否限定范围
	 */
	public boolean isScoped() {
		return clientDetailInfo.isScoped();
	}

	/**
	 * 获取该客户端权限范围
	 *
	 * @return 权限范围集合
	 */
	public Set<String> getScope() {
		return clientDetailInfo.getScopes();
	}

	/**
	 * 获取该客户端的授权类型
	 *
	 * @return 授权类型集合
	 */
	public Set<String> getAuthorizedGrantTypes() {
		return clientDetailInfo.getAuthorizedGrantTypes();
	}

	/**
	 * 获取该客户端回调url，授权server会将code传递该地址，该地址需拿该code换取token
	 *
	 * @return 回调URL集合
	 */
	public Set<String> getRegisteredRedirectUri() {
		return clientDetailInfo.getCodeToTokenUrl();
	}

	/**
	 * 获取该客户端权限
	 *
	 * @return 权限集合
	 */
	public Collection<GrantedAuthority> getAuthorities() {
		List<GrantedAuthority> auths = new LinkedList<>();
		if (clientDetailInfo.getAthorities() != null) {
			clientDetailInfo.getAthorities().forEach(auth -> {
				GrantedAuthority dto = new SimpleGrantedAuthority(auth);
				auths.add(dto);
			});
		}
		return auths;
	}

	/**
	 * 客户端访问令牌有效期
	 *
	 * @return 访问令牌有效期（秒）
	 */
	public Integer getAccessTokenValiditySeconds() {
		return clientDetailInfo.getAccessTokenValiditySeconds().intValue();
	}

	/**
	 * 客户端刷新令牌有效期
	 *
	 * @return 刷新令牌有效期（秒）
	 */
	public Integer getRefreshTokenValiditySeconds() {
		return clientDetailInfo.getRefreshTokenValiditySeconds().intValue();
	}

	/**
	 * 测试客户端是否需要特定范围的用户批准
	 *
	 * @param scope 范围
	 * @return 是否自动批准
	 */
	public boolean isAutoApprove(String scope) {
		return clientDetailInfo.isAutoApprove();
	}

	/**
	 * 客户端附加信息
	 *
	 * @return 附加信息
	 */
	public Map<String, Object> getAdditionalInformation() {
		return clientDetailInfo.getAttrs();
	}

	/**
	 * 获取客户端主页地址
	 *
	 * @return 客户端主页地址
	 */
	public String getClientIndexPage() {
		Map<String, Object> attrs = this.getAdditionalInformation();
		return (String) attrs.get("clientIndexUrl");
	}

	/**
	 * 设置客户端主页地址
	 *
	 * @param url 客户端主页地址
	 */
	public void setClientIndexPage(String url) {
		Map<String, Object> attrs = this.getAdditionalInformation();
		attrs.put("clientIndexUrl", url);
	}
}

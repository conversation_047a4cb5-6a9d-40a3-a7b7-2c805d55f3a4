(function(e){function t(t){for(var r,i,s=t[0],l=t[1],c=t[2],u=0,p=[];u<s.length;u++)i=s[u],Object.prototype.hasOwnProperty.call(n,i)&&n[i]&&p.push(n[i][0]),n[i]=0;for(r in l)Object.prototype.hasOwnProperty.call(l,r)&&(e[r]=l[r]);d&&d(t);while(p.length)p.shift()();return o.push.apply(o,c||[]),a()}function a(){for(var e,t=0;t<o.length;t++){for(var a=o[t],r=!0,s=1;s<a.length;s++){var l=a[s];0!==n[l]&&(r=!1)}r&&(o.splice(t--,1),e=i(i.s=a[0]))}return e}var r={},n={app:0},o=[];function i(t){if(r[t])return r[t].exports;var a=r[t]={i:t,l:!1,exports:{}};return e[t].call(a.exports,a,a.exports,i),a.l=!0,a.exports}i.m=e,i.c=r,i.d=function(e,t,a){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:a})},i.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var a=Object.create(null);if(i.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)i.d(a,r,function(t){return e[t]}.bind(null,r));return a},i.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="";var s=window["webpackJsonp"]=window["webpackJsonp"]||[],l=s.push.bind(s);s.push=t,s=s.slice();for(var c=0;c<s.length;c++)t(s[c]);var d=l;o.push([0,"chunk-vendors"]),a()})({0:function(e,t,a){e.exports=a("56d7")},"034f":function(e,t,a){"use strict";var r=a("3ebb"),n=a.n(r);n.a},1149:function(e,t,a){"use strict";var r=a("13c3"),n=a.n(r);n.a},"11df":function(e,t,a){var r=a("dc2a");"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var n=a("499e").default;n("97cceab4",r,!0,{sourceMap:!1,shadowMode:!1})},"13c3":function(e,t,a){var r=a("5f05");"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var n=a("499e").default;n("06203bb6",r,!0,{sourceMap:!1,shadowMode:!1})},"193d":function(e,t,a){"use strict";var r=a("8f05"),n=a.n(r);n.a},"3ebb":function(e,t,a){var r=a("e31a");"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var n=a("499e").default;n("517436b3",r,!0,{sourceMap:!1,shadowMode:!1})},5430:function(e,t,a){"use strict";var r=a("8822"),n=a.n(r);n.a},"56d7":function(e,t,a){"use strict";a.r(t);a("e260"),a("e6cf"),a("cca6"),a("a79d");var r=a("2b0e"),n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{attrs:{id:"app"}},[a("router-view")],1)},o=[],i={name:"Main"},s=i,l=(a("034f"),a("2877")),c=Object(l["a"])(s,n,o,!1,null,null,null),d=c.exports,u=(a("db4d"),a("c975"),a("91d0")),p=a.n(u),f=p.a.getLanguageList(),m=localStorage.getItem("language");if(m&&f.indexOf(m)>-1)p.a.setDefaultLanguage(m);else{var g=p.a.getDefaultLanguage();localStorage.setItem("language",g)}document.title=p.a.get("COMMON_LOGIN");var h={get:function(){var e="";try{e=p.a.get.apply(p.a,arguments)}catch(t){e=(arguments.length<=0?void 0:arguments[0])||"NO KEY"}return e},getDefaultLanguage:function(){return p.a.getDefaultLanguage()},install:function(e){e.mixin({filters:{TL:function(e){return h.get(e)}},methods:{$Lag:h.get}})}},v=h,b=a("3ca4"),x=a.n(b),y=a("5470"),w=a.n(y),C=localStorage.getItem("language");"en"===C?r["default"].use(x.a,{locale:w.a}):r["default"].use(x.a),r["default"].use(v);var T=a("8c4f"),_=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"main-box"},[e.load?a("p",{staticClass:"load-box"},[a("Spin")],1):[a("LeftCom",{attrs:{"bd-img":e.logoPic,"tip-img":e.tipPic}}),a("RightCom",{attrs:{"target-url":e.targetUrl,"text-pic":e.textPic,"multi-tenant-status":e.multiTenantStatus,"tenant-domain-list":e.tenantDomainList}})]],2)},k=[],O=(a("caad"),a("6eba"),a("0d03"),a("ac1f"),a("2532"),a("841c"),a("1276"),a("96cf"),a("1da1")),D=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"left-box",style:{backgroundImage:"url("+e.bdImg+")",color:"red"}},[a("div",{staticClass:"tip-box"},[a("img",{attrs:{src:e.tipImg}})])])},S=[],I={props:{bdImg:{type:String,default:""},tipImg:{type:String,default:""}}},M=I,L=(a("193d"),Object(l["a"])(M,D,S,!1,null,"6239d36a",null)),F=L.exports,E=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"right-box"},[a("div",{staticClass:"form-box"},[a("p",{staticClass:"img-box"},[a("img",{staticClass:"img",attrs:{src:e.textPic,alt:""}})]),a("Form",{ref:"loginForm",attrs:{"label-position":"top",rules:e.formValidate,model:e.loginForm}},[e.multiTenantStatus?a("FormItem",{attrs:{prop:"domainId"}},[a("template",{slot:"label"},[a("span",{staticClass:"label"},[e._v("租户域")])]),a("Select",{model:{value:e.loginForm.domainId,callback:function(t){e.$set(e.loginForm,"domainId",t)},expression:"loginForm.domainId"}},e._l(e.tenantDomainList,(function(t){return a("Option",{key:t.id,attrs:{value:t.id}},[e._v(" "+e._s(t.name)+" ")])})),1)],2):e._e(),a("FormItem",{attrs:{prop:"loginCode",error:e.error3}},[a("template",{slot:"label"},[a("span",{staticClass:"label"},[e._v(e._s(e._f("TL")("COMMON_USERNAME")))])]),a("i-input",{attrs:{size:"large",autofocus:"",name:"username",autocomplete:"off"},on:{"on-enter":e.getFormData},model:{value:e.loginForm.loginCode,callback:function(t){e.$set(e.loginForm,"loginCode","string"===typeof t?t.trim():t)},expression:"loginForm.loginCode"}})],2),a("FormItem",{attrs:{prop:"password",error:e.error1}},[a("template",{slot:"label"},[a("span",{staticClass:"label"},[e._v(e._s(e._f("TL")("COMMON_PASSWORD")))])]),a("i-input",{attrs:{size:"large",type:"password",name:"password",autocomplete:"off"},on:{"on-enter":e.getFormData},model:{value:e.loginForm.password,callback:function(t){e.$set(e.loginForm,"password","string"===typeof t?t.trim():t)},expression:"loginForm.password"}})],2),e.showCapt?a("FormItem",{attrs:{prop:"captchaCode",error:e.error2}},[a("i-input",{staticStyle:{width:"140px"},attrs:{size:"large",name:"validCode",autocomplete:"off",placeholder:e.captcha},on:{"on-enter":e.getFormData},model:{value:e.loginForm.captchaCode,callback:function(t){e.$set(e.loginForm,"captchaCode","string"===typeof t?t.trim():t)},expression:"loginForm.captchaCode"}}),a("img",{staticClass:"captImg",attrs:{src:e.captUrl,alt:"点击重新获取"},on:{click:e.getNewCapt}})],1):e._e(),a("FormItem",[e.errorMes?a("p",{staticClass:"loginError"},[e._v(" "+e._s(e.errorMes)+" ")]):e._e(),a("Button",{staticClass:"login-button",attrs:{type:"primary",long:"",loading:e.loginLoad},on:{click:e.getFormData}},[e._v(" "+e._s(e._f("TL")("COMMON_LOGIN"))+" ")])],1)],1),a("p",{staticClass:"text-box"},[a("span",{class:{active:"zh"===e.language},on:{click:function(){return e.changeLanguage("zh")}}},[e._v(" 中文 ")]),e._v(" | "),a("span",{class:{active:"en"===e.language},on:{click:function(){return e.changeLanguage("en")}}},[e._v(" English ")])])],1),a("form",{ref:"trueForm",attrs:{action:"/oauth/login",method:"POST"}},[e.multiTenantStatus?a("select",{directives:[{name:"model",rawName:"v-model.trim",value:e.realForm.domainId,expression:"realForm.domainId",modifiers:{trim:!0}}],staticStyle:{display:"none"},attrs:{name:"domainId"},on:{change:function(t){var a=Array.prototype.filter.call(t.target.options,(function(e){return e.selected})).map((function(e){var t="_value"in e?e._value:e.value;return t}));e.$set(e.realForm,"domainId",t.target.multiple?a:a[0])}}},e._l(e.tenantDomainList,(function(t){return a("option",{key:t.id,domProps:{value:t.id}},[e._v(" "+e._s(t.name)+" ")])})),0):e._e(),a("input",{directives:[{name:"model",rawName:"v-model.trim",value:e.realForm.loginCode,expression:"realForm.loginCode",modifiers:{trim:!0}}],attrs:{type:"hidden",name:"username"},domProps:{value:e.realForm.loginCode},on:{input:function(t){t.target.composing||e.$set(e.realForm,"loginCode",t.target.value.trim())},blur:function(t){return e.$forceUpdate()}}}),a("input",{directives:[{name:"model",rawName:"v-model.trim",value:e.realForm.password,expression:"realForm.password",modifiers:{trim:!0}}],attrs:{type:"hidden",name:"password"},domProps:{value:e.realForm.password},on:{input:function(t){t.target.composing||e.$set(e.realForm,"password",t.target.value.trim())},blur:function(t){return e.$forceUpdate()}}}),e.showCapt?a("input",{directives:[{name:"model",rawName:"v-model.trim",value:e.realForm.captchaCode,expression:"realForm.captchaCode",modifiers:{trim:!0}}],attrs:{type:"hidden",name:"validCode"},domProps:{value:e.realForm.captchaCode},on:{input:function(t){t.target.composing||e.$set(e.realForm,"captchaCode",t.target.value.trim())},blur:function(t){return e.$forceUpdate()}}}):e._e()])])},R=[],U=(a("99af"),a("7db0"),a("d3b7"),a("e25e"),a("25f0"),a("5319"),a("4795"),a("a2c5"));function N(e){return e<10?"0"+e:e}function $(e){var t,a,r,n,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"HH:mm:ss";return t=Math.floor(e/1e3),a=N(Math.floor(t/3600)),r=N(Math.floor(t%3600/60)),n=N(Math.floor(t%60)),o.replace("HH",a).replace("mm",r).replace("ss",n)}var z={props:{targetUrl:{type:String,default:""},textPic:{type:String,default:""},multiTenantStatus:{type:Boolean,default:!1},tenantDomainList:{type:Array,default:function(){return[]}}},data:function(){return{loginLoad:!1,language:v.getDefaultLanguage(),captUrl:"/oauth/user/getValidCodeImg",captcha:v.get("BASE_VERIFICATION_CODE"),loginForm:{loginCode:"",password:"",captchaCode:"",domainId:""},realForm:{loginCode:"",password:"",captchaCode:"",domainId:""},formValidate:{},resConfig:{},errorMes:!1,error1_text:!1,error2_text:!1,error3_text:!1,djs:""}},computed:{showCapt:function(){return this.resConfig.valid&&1===this.resConfig.valid},error1:function(){return this.error1_text?"".concat(this.error1_text,", 还可重试 ").concat(this.resConfig.allowNum," 次"):""},error2:function(){return this.error2_text},error3:function(){return this.error3_text?"".concat(this.error3_text,", 距解锁还有：").concat(this.djs):""}},watch:{loginForm:{handler:function(){this.errorMes=!1},deep:!0}},created:function(){var e=this;this.init();var t=window.localStorage.getItem("loginResConfig");t&&(this.resConfig=JSON.parse(t));var a=window.localStorage.getItem("loginResConfigTs");a=a?parseInt(a):0;var r=864e5,n=Date.now();if((!a||a+r<n)&&this.resConfig.unlockTime<n&&(window.localStorage.removeItem("loginResConfig"),window.localStorage.removeItem("loginResConfigTs"),this.resConfig={}),this.resConfig.error&&(1===this.resConfig.error&&(this.error1_text=this.$Lag("DCV_USERNAME_PASSWORD_ERROR")),2===this.resConfig.error&&(this.error2_text=this.$Lag("BASE_INCORRECT_VERIFICATION_CODE")),3===this.resConfig.error)){this.error3_text="用户已锁定";var o=function(){var t=e.autoDuration();e.djs=t,t||(e.error3_text=!1,clearInterval(i))};o();var i=setInterval((function(){o()}),1e3)}},methods:{init:function(){this.initFormValidate();var e=this.tenantDomainList.find((function(e){return e.id===JSON.parse(localStorage.getItem("domainId")||null)}));this.loginForm.domainId=e?e.id:this.tenantDomainList.length?this.tenantDomainList[0].id:""},initFormValidate:function(){var e=this,t=this;this.formValidate={loginCode:[{validator:function(e,t){return""!==t},message:this.$Lag("COMMON_USERNAME_CANNOT_BE_EMPTY"),trigger:"blur"},{validator:function(e,a){return!(t.multiTenantStatus&&!t.loginForm.domainId)||"superadmin"===a},message:"不选择租户域则只能使用superadmin登录",trigger:"blur"}],password:[{validator:function(e,t){return""!==t},message:this.$Lag("COMMON_PASSWROD_CANNOT_BE_BLANK"),trigger:"blur"}],captchaCode:[{validator:function(t,a){return!e.showCapt||""!==a},message:this.$Lag("BASE_VERIFICATION_CODE_CANNOT_BE_EMPTY"),trigger:"blur"}]}},getFormData:function(){var e=this;return Object(O["a"])(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$refs.loginForm.validate();case 2:a=t.sent,a&&(localStorage.setItem("domainId",e.loginForm.domainId),e.realSubmit());case 4:case"end":return t.stop()}}),t)})))()},changePassWordToBase:function(e){var t=U["enc"].Utf8.parse("base@uin"),a=U["DES"].encrypt(e,t,{mode:U["mode"].ECB,padding:U["pad"].Pkcs7});return a.ciphertext.toString(U["enc"].Base64)},changeLanguage:function(e){e!==this.language&&(localStorage.setItem("language",e),window.location.reload())},getNewCapt:function(){this.captUrl="/oauth/user/getValidCodeImg?".concat((new Date).getTime())},autoDuration:function(){if(!this.resConfig.unLockDuration)return"";var e=Date.now(),t=this.resConfig.unlockTime-e;return!(t<=1e3)&&$(t)},realSubmit:function(){var e=this;this.$set(this.realForm,"loginCode",this.loginForm.loginCode),this.$set(this.realForm,"password",this.changePassWordToBase(this.loginForm.password)),this.$set(this.realForm,"captchaCode",this.loginForm.captchaCode),this.$set(this.realForm,"domainId",this.loginForm.domainId),this.$nextTick((function(){e.$refs.trueForm.submit()}))}}},V=z,j=(a("1149"),Object(l["a"])(V,E,R,!1,null,"60d531d6",null)),P=j.exports,A=a("53ca");function B(e,t,a,r){var n={"Content-Type":"application/json;charset=UTF-8","X-Requested-With":"XMLHttpRequest",language:v.getDefaultLanguage(),Connection:"keep-alive",REQUEST_HEADER:"binary-http-client-header",requestId:Math.random()},o=a&&a["Content-Type"]&&a["Content-Type"].indexOf("form-data")>-1,i={body:null!==e?o?e:"object"===Object(A["a"])(e)?JSON.stringify(e):e:"",method:"POST",credentials:"include",headers:n};return Object.assign(n,a),Object.assign(i,t,r),o&&delete n["Content-Type"],{init:i}}function G(e){return W.apply(this,arguments)}function W(){return W=Object(O["a"])(regeneratorRuntime.mark((function e(t){var a,r,n,o,i,s,l,c,d=arguments;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=d.length>1&&void 0!==d[1]?d[1]:null,r=d.length>2&&void 0!==d[2]?d[2]:{},n=d.length>3&&void 0!==d[3]?d[3]:{},o=d.length>4&&void 0!==d[4]?d[4]:{},i=B(a,r,n,o),s=i.init,e.prev=5,e.next=8,fetch(t,s).then((function(e){return e}));case 8:return l=e.sent,c={},c.headers=l.headers,e.next=13,l.json();case 13:return c.res=e.sent,e.abrupt("return",c.res.data);case 17:return e.prev=17,e.t0=e["catch"](5),e.abrupt("return",!1);case 20:case"end":return e.stop()}}),e,null,[[5,17]])}))),W.apply(this,arguments)}function H(){return q.apply(this,arguments)}function q(){return q=Object(O["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,G("/oauth/sys/getLogos");case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)}))),q.apply(this,arguments)}function J(){return K.apply(this,arguments)}function K(){return K=Object(O["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,G("/oauth/sys/multiTenantStatus");case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)}))),K.apply(this,arguments)}function Q(){return Y.apply(this,arguments)}function Y(){return Y=Object(O["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,G("/oauth/sys/queryList");case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)}))),Y.apply(this,arguments)}var X={components:{RightCom:P,LeftCom:F},data:function(){return{load:!0,logoPic:"",tipPic:"",textPic:"",targetUrl:"",multiTenantStatus:!1,tenantDomainList:[]}},created:function(){this.init(),this.getQueryVariable(),this.reUrl()},methods:{init:function(){var e=this;return Object(O["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getLogos();case 2:if(e.$route.path.includes("super-admin")){t.next=7;break}return t.next=5,e.getMultiTenantStatus();case 5:return t.next=7,e.queryTenantDomainList();case 7:e.load=!1;case 8:case"end":return t.stop()}}),t)})))()},getLogos:function(){var e=this;return Object(O["a"])(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,H();case 2:a=t.sent,a&&(e.logoPic=a.login_bd.url,e.tipPic=a.coverup_login_bd.url,e.textPic=a.login.url,document.querySelector("#fav-box").setAttribute("href",a.tag.url));case 4:case"end":return t.stop()}}),t)})))()},getMultiTenantStatus:function(){var e=this;return Object(O["a"])(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,J();case 2:a=t.sent,e.multiTenantStatus=a;case 4:case"end":return t.stop()}}),t)})))()},queryTenantDomainList:function(){var e=this;return Object(O["a"])(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Q();case 2:a=t.sent,e.tenantDomainList=a||[];case 4:case"end":return t.stop()}}),t)})))()},getQueryVariable:function(){for(var e={},t=window.location.search.substring(1),a=t.split("&"),r=0;r<a.length;r++){var n=a[r].split("=");e[n[0]]=Math.floor(n[1])}e.unlockTime=e.unLockDuration?Date.now()+e.unLockDuration:0,e.error?(window.localStorage.setItem("loginResConfig",JSON.stringify(e)),window.localStorage.setItem("loginResConfigTs",Date.now())):(window.localStorage.removeItem("loginResConfig"),window.localStorage.removeItem("loginResConfigTs"))},reUrl:function(){window.history.pushState(null,document.title,window.location.href.split("?")[0])}}},Z=X,ee=(a("5430"),Object(l["a"])(Z,_,k,!1,null,"fe5fc8a8",null)),te=ee.exports,ae=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"client-container"},[a("Row",{staticClass:"client-header"},[a("i-col",{attrs:{span:"12"}},[a("Button",{staticClass:"btn-group",attrs:{type:"primary"},on:{click:e.addClient}},[a("Icon",{style:{"font-size":"14px","margin-right":"5px","margin-top":"-2px","margin-left":"-2px"},attrs:{custom:"ts ts-add"}}),e._v("新建 ")],1)],1),a("i-col",{staticClass:"align-right",attrs:{span:"12"}},[a("i-input",{staticStyle:{width:"300px"},attrs:{placeholder:"搜索客户端id",search:"",clearable:""},on:{"on-search":e.search,"on-clear":e.search},model:{value:e.keyword,callback:function(t){e.keyword="string"===typeof t?t.trim():t},expression:"keyword"}})],1)],1),a("div",{staticClass:"client-main"},[a("div",{ref:"client-table",staticClass:"client-table"},[a("Table",{attrs:{columns:e.tableColumns,data:e.tableData,"max-height":e.height,loading:e.tableLoading,"row-class-name":e.rowClassName,"refresh-to-top":"","column-control":"",border:""},scopedSlots:e._u([{key:"action",fn:function(t){var r=t.row,n=t.index;return[a("Button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.showEdit(r)}}},[e._v(" 编辑 ")]),a("Button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.remove(r,n)}}},[e._v(" 删除 ")])]}}])}),e.searchNoData?a("p",{staticClass:"noData"},[e._v(" "+e._s(e._f("TL")("COMMON_NO_DATA"))+" ")]):e._e()],1),a("Modal",{attrs:{width:"400",title:e._f("TL")("COMMON_DELETE")},model:{value:e.removeModalVis,callback:function(t){e.removeModalVis=t},expression:"removeModalVis"}},[a("div",{staticClass:"pop-min-title",staticStyle:{"text-align":"center"}},[a("p",[e._v(e._s(e._f("TL")("COMMON_PLEASE_CONFIRM_WHETHER_TO_DELETE")))])]),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("Button",{attrs:{type:"primary"},on:{click:e.removeOnOk}},[e._v(" "+e._s(e._f("TL")("COMMON_OK"))+" ")]),a("Button",{on:{click:function(t){e.removeModalVis=!1}}},[e._v(" "+e._s(e._f("TL")("COMMON_CANCEL"))+" ")])],1)]),a("Modal",{attrs:{"class-name":"vertical-center-modal","mask-closable":!1,width:"460",title:"客户端信息"},model:{value:e.editModalVisiable,callback:function(t){e.editModalVisiable=t},expression:"editModalVisiable"}},[a("div",{staticClass:"addoredit"},[a("Form",{ref:"formEditor",attrs:{model:e.editorData,"label-width":e.labelWidth,rules:e.ruleValidate}},[a("FormItem",{attrs:{label:"客户端id",prop:"clientCode"}},[a("Input",{attrs:{maxlength:50,disabled:null!=e.editorData.id},model:{value:e.editorData.clientCode,callback:function(t){e.$set(e.editorData,"clientCode",t)},expression:"editorData.clientCode"}})],1),a("FormItem",{directives:[{name:"show",rawName:"v-show",value:null!=e.editorData.id,expression:"editorData.id!=null"}],attrs:{label:"客户端加密串"}},[a("Input",{attrs:{maxlength:50,disabled:null!=e.editorData.id},model:{value:e.editorData.clientSecret,callback:function(t){e.$set(e.editorData,"clientSecret",t)},expression:"editorData.clientSecret"}})],1),a("FormItem",{attrs:{label:"客户端回调地址"},model:{value:e.editorData.codeToTokenUrl,callback:function(t){e.$set(e.editorData,"codeToTokenUrl",t)},expression:"editorData.codeToTokenUrl"}},[a("Input",{attrs:{maxlength:100,value:"http://www.baidu.com"},model:{value:e.editorData.codeToTokenUrl[0],callback:function(t){e.$set(e.editorData.codeToTokenUrl,0,t)},expression:"editorData.codeToTokenUrl[0]"}})],1),a("FormItem",{attrs:{label:"客户端主页地址"},model:{value:e.editorData.clientIndexUrl,callback:function(t){e.$set(e.editorData,"clientIndexUrl",t)},expression:"editorData.clientIndexUrl"}},[a("Input",{attrs:{maxlength:100,value:"http://www.baidu.com"},model:{value:e.editorData.clientIndexUrl,callback:function(t){e.$set(e.editorData,"clientIndexUrl",t)},expression:"editorData.clientIndexUrl"}})],1),a("FormItem",{attrs:{label:"token有效时间",prop:"accessTokenValiditySeconds",maxlength:50,name:e._f("TL")("COMMON_EMAIL")}},[a("Input",{model:{value:e.editorData.accessTokenValiditySeconds,callback:function(t){e.$set(e.editorData,"accessTokenValiditySeconds",t)},expression:"editorData.accessTokenValiditySeconds"}})],1),a("FormItem",{attrs:{label:"refresh_token有效时间",prop:"refreshTokenValiditySeconds"}},[a("Input",{attrs:{maxlength:50},model:{value:e.editorData.refreshTokenValiditySeconds,callback:function(t){e.$set(e.editorData,"refreshTokenValiditySeconds",t)},expression:"editorData.refreshTokenValiditySeconds"}})],1),a("FormItem",{attrs:{label:"授权方式",prop:"authorizedGrantTypes"}},[a("Select",{attrs:{multiple:"",filterable:"","max-tag-count":4},model:{value:e.editorData.authorizedGrantTypes,callback:function(t){e.$set(e.editorData,"authorizedGrantTypes",t)},expression:"editorData.authorizedGrantTypes"}},e._l(e.authorizedTypes,(function(t){return a("Option",{key:t,attrs:{value:t}},[e._v(" "+e._s(t)+" ")])})),1)],1),a("FormItem",{attrs:{label:"scopes"}},[a("Select",{attrs:{transfer:"",multiple:"",filterable:"","max-tag-count":4},model:{value:e.editorData.scopes,callback:function(t){e.$set(e.editorData,"scopes",t)},expression:"editorData.scopes"}},e._l(e.scopes,(function(t){return a("Option",{key:t,attrs:{value:t}},[e._v(" "+e._s(t)+" ")])})),1)],1)],1)],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("Button",{attrs:{type:"primary"},on:{click:e.saveEdit}},[e._v(" "+e._s(e._f("TL")("COMMON_OK"))+" ")]),a("Button",{on:{click:e.closeEdit}},[e._v(" "+e._s(e._f("TL")("COMMON_CANCEL"))+" ")])],1)])],1)],1)},re=[],ne=(a("4de4"),a("a15b"),a("d81d"),{name:"Client",components:{},props:{noPage:{type:Boolean,default:!1},preUrl:{type:String,default:"/oauth"},saveUrl:{type:String,default:"/client/register"},queryUrl:{type:String,default:"/client/getList"},deleteByIdUrl:{type:String,default:"/client/removeClientInfoByCode"}},data:function(){return{curUser:{},labelWidth:80,height:800,tableColumns:[{title:"序号",align:"left",type:"index",width:60,indexMethod:function(e){return e._index+1},control:!1},{title:"客户端id",key:"clientCode",align:"left",minWidth:80,resizable:!0,tooltip:!0},{title:"客户端加密串",key:"clientSecret",align:"left",minWidth:80,resizable:!0,tooltip:!0},{title:"回调地址",key:"codeToTokenUrls",align:"left",minWidth:80,resizable:!0,tooltip:!0},{title:"主页地址",key:"clientIndexUrl",align:"left",minWidth:80,resizable:!0,tooltip:!0},{title:"token有效时间",key:"accessTokenValiditySeconds",align:"left",minWidth:80,resizable:!0,tooltip:!0},{title:"refresh_token有效时间",key:"refreshTokenValiditySeconds",align:"left",minWidth:80,resizable:!0,tooltip:!0},{title:"授权方式",key:"authorizedGrantTypess",align:"left",minWidth:80,resizable:!0,tooltip:!0},{title:"scopes",key:"scopess",align:"left",minWidth:80,resizable:!0,tooltip:!0},{title:"操作",slot:"action",minWidth:160,align:"left",control:!1}],tableData:[],tableLoading:!0,keyword:"",pagination:{pageNum:1,pageSize:30,totalPages:1,totalRows:1},editorData:{clientCode:"",secretRequired:"true",clientSecret:"",codeToTokenUrl:[],clientIndexUrl:"",accessTokenValiditySeconds:"",refreshTokenValiditySeconds:"",authorizedGrantTypes:[],scoped:"true",scopes:[],autoApprove:"true"},editModalVisiable:!1,ruleValidate:{accessTokenValiditySeconds:[{pattern:/^[0-9]{0,50}$/,message:"只允许输入数字",trigger:"blur"}],refreshTokenValiditySeconds:[{pattern:/^[0-9]{0,50}$/,message:"只允许输入数字",trigger:"blur"}],clientCode:[{required:!0,message:"请输入客户端id"}],codeToTokenUrl:[{required:!0,message:"请输入回调地址"}],clientIndexUrl:[{required:!0,message:"请输入主页地址"}],clientSecret:[{required:!0,message:"请输入加密串"}]},removeModalVis:!1,resetPwdVis:!1,operateItem:{},typesSelect:[],percent:0,loading:!1,upAttrInfoShow:!1,upAttrInfo:null,authorizedTypes:["authorization_code","client_credentials","password","refresh_token"],scopes:["tarsier-comm","tarsier"]}},computed:{searchNoData:function(){var e=this;return 0===this.tableData.length||0==this.tableData.filter((function(t){return t.clientCode.toLocaleLowerCase().indexOf(e.keyword.toLocaleLowerCase())>-1})).length}},watch:{typesSelect:{handler:function(e){this.editorData.authorizedGrantTypes=[];for(var t=0;t<this.authorizedGrantTypes.length;t+=1)-1!==e.indexOf(this.authorizedGrantTypes[t].id)&&this.editorData.authorizedGrantTypes.push(this.authorizedGrantTypes[t])},deep:!0}},created:function(){this.getDatas()},mounted:function(){this.resizeTableHeight(),window.addEventListener("resize",this.resizeTableHeight)},beforeDestroy:function(){window.removeEventListener("resize",this.resizeTableHeight)},methods:{rowClassName:function(e){return""===this.keyword||""===e.clientCode?"":e.clientCode.toLocaleLowerCase().indexOf(this.keyword.toLocaleLowerCase())<0?"hideRow":""},resizeTableHeight:function(){this.height=this.$refs["client-table"].offsetHeight},search:function(){},getDatas:function(){var e=this;return Object(O["a"])(regeneratorRuntime.mark((function t(){var a,r,n,o,i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.tableLoading=!0,Object.assign({},{keyword:e.keyword},e.pagination),t.next=4,G(e.preUrl+e.queryUrl);case 4:a=t.sent,e.tableData=a.map((function(e){return e.attrs.clientIndexUrl&&(e.clientIndexUrl=e.attrs.clientIndexUrl),e.codeToTokenUrl&&(e.codeToTokenUrls=e.codeToTokenUrl.join(",")),e.authorizedGrantTypes&&(e.authorizedGrantTypess=e.authorizedGrantTypes.join(",")),e.scopes&&(e.scopess=e.scopes.join(",")),e})),r=a.pageNum,n=a.pageSize,o=a.totalPages,i=a.totalRows,e.pagination={pageNum:r,pageSize:n,totalPages:o,totalRows:i},e.tableLoading=!1;case 9:case"end":return t.stop()}}),t)})))()},showEdit:function(e){this.$refs["formEditor"].resetFields();var t=this.setTidyClients(e,1);return this.editorData=t,this.editModalVisiable=!0,e},setTidyClients:function(e,t){var a={clientCode:e.clientCode,clientSecret:e.clientSecret,codeToTokenUrl:e.codeToTokenUrl,clientIndexUrl:e.clientIndexUrl,accessTokenValiditySeconds:e.accessTokenValiditySeconds,scopes:e.scopes,refreshTokenValiditySeconds:e.refreshTokenValiditySeconds,authorizedGrantTypes:e.authorizedGrantTypes,id:e.id};return a},addClient:function(){this.editorData={},this.$refs["formEditor"].resetFields(),this.editorData.authorizedGrantTypes=[],this.editorData.codeToTokenUrl=[],this.editorData.scopes=[],this.editModalVisiable=!0},saveEdit:function(){var e=this;return this.$refs["formEditor"].validate(function(){var t=Object(O["a"])(regeneratorRuntime.mark((function t(a){var r,n,o;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a){t.next=10;break}return r={},r=e.editorData.id?e.editorData:e.setTidyClients(e.editorData,2),n={clientIndexUrl:r.clientIndexUrl},r.attrs=n,delete r.clientIndexUrl,t.next=8,G(e.preUrl+e.saveUrl,r);case 8:o=t.sent,o&&(e.editModalVisiable=!1,e.getDatas(e.pagination.pageNum),e.handleReset("formEditor"),e.editorData.codeToTokenUrl?e.$emit("add-client",r):(e.$emit("edit-client",r),e.curUser&&r.id===e.curUser.id&&e.$emit("edit-self",r)));case 10:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()),!0},closeEdit:function(){this.editModalVisiable=!1},remove:function(e){var t=this;this.operateItem=e,this.$Modal.confirm({title:"Title",content:"<p>"+this.$Lag("COMMON_PLEASE_CONFIRM_WHETHER_TO_DELETE")+"</p>",onOk:function(){t.removeOnOk()}})},removeOnOk:function(){var e=this;return Object(O["a"])(regeneratorRuntime.mark((function t(){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=e.operateItem,console.log(a.clientCode),t.next=4,G(e.preUrl+e.deleteByIdUrl,a.clientCode);case 4:if(r=t.sent,!r){t.next=8;break}return e.getDatas(),t.abrupt("return",!0);case 8:return t.abrupt("return",!1);case 9:case"end":return t.stop()}}),t)})))()},handleReset:function(e){this.$refs[e].resetFields()}}}),oe=ne,ie=(a("aab4"),a("fd96"),Object(l["a"])(oe,ae,re,!1,null,"fdea79f0",null)),se=ie.exports;r["default"].use(T["a"]);var le=new T["a"]({routes:[{path:"/",name:"login",component:te},{path:"/client",name:"client",component:se},{path:"/super-admin",name:"login",component:te}]});r["default"].config.productionTip=!1,new r["default"]({router:le,render:function(e){return e(d)}}).$mount("#app")},"5f05":function(e,t,a){var r=a("24fb");t=r(!1),t.push([e.i,".right-box[data-v-60d531d6]{width:680px;height:100%;vertical-align:middle;background:#fff;display:flex;flex-direction:row;justify-content:center;align-items:center}.right-box .img-box[data-v-60d531d6]{text-align:center}.right-box .img-box .img[data-v-60d531d6]{max-width:150px;max-height:150px}.right-box .form-box[data-v-60d531d6]{width:400px}.right-box .label[data-v-60d531d6]{font-size:14px}.right-box .captImg[data-v-60d531d6]{display:inline-block;width:101px;height:40px;padding-left:10px;vertical-align:middle}.right-box .login-button[data-v-60d531d6]{margin-top:10px}.right-box .loginError[data-v-60d531d6]{color:#f55c3d;font-weight:bolder}.right-box .text-box[data-v-60d531d6]{text-align:center;margin-top:20px;cursor:pointer}.right-box .text-box span[data-v-60d531d6]{padding:0 4px}.right-box .text-box .active[data-v-60d531d6]{color:#ff7a1e}",""]),e.exports=t},6772:function(e,t,a){var r=a("24fb");t=r(!1),t.push([e.i,".left-box[data-v-6239d36a]{display:flex;flex:1;width:100%;height:100%;position:relative;background-position:50%;background-size:cover;overflow:hidden}.left-box[data-v-6239d36a],.tip-box[data-v-6239d36a]{background-repeat:no-repeat}.tip-box[data-v-6239d36a]{position:absolute;top:30%;left:20%;width:340px;height:230px;background-size:contain}.tip-box img[data-v-6239d36a]{max-width:100%;max-height:100%}",""]),e.exports=t},"74f4":function(e,t,a){var r=a("b100");"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var n=a("499e").default;n("76798dc4",r,!0,{sourceMap:!1,shadowMode:!1})},8822:function(e,t,a){var r=a("ca38");"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var n=a("499e").default;n("efd406ce",r,!0,{sourceMap:!1,shadowMode:!1})},"8f05":function(e,t,a){var r=a("6772");"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var n=a("499e").default;n("e0688980",r,!0,{sourceMap:!1,shadowMode:!1})},a2c5:function(e,t){e.exports=CryptoJS},aab4:function(e,t,a){"use strict";var r=a("74f4"),n=a.n(r);n.a},b100:function(e,t,a){var r=a("24fb");t=r(!1),t.push([e.i,"#app,body,html{height:100%}",""]),e.exports=t},ca38:function(e,t,a){var r=a("24fb");t=r(!1),t.push([e.i,".main-box[data-v-fe5fc8a8]{width:100%;height:100%;box-sizing:border-box;display:flex}.load-box[data-v-fe5fc8a8]{margin:20px 50%}",""]),e.exports=t},dc2a:function(e,t,a){var r=a("24fb");t=r(!1),t.push([e.i,".client-container[data-v-fdea79f0]{display:flex;flex-direction:column;padding:20px;background:#fff;height:100%}.client-main[data-v-fdea79f0]{flex:1 1 auto;display:flex;flex-direction:column}.client-main .client-table[data-v-fdea79f0]{flex:1 1 auto;border-bottom:1px solid #ebebeb}.client-main .client-table .ivu-btn-text[data-v-fdea79f0]{padding:0;margin-right:6px}.client-main .client-table .ivu-btn-text[data-v-fdea79f0]:last-child{margin:0}.client-main .client-table .noData[data-v-fdea79f0]{border:1px solid #ebebeb;text-align:center;line-height:40px;border-top:0}.client-header[data-v-fdea79f0]{margin-bottom:14px}.client-header .btn-group+.btn-group[data-v-fdea79f0]{margin-left:10px}.client-header .f18[data-v-fdea79f0]{font-size:18px}.align-right[data-v-fdea79f0]{text-align:right}.ivu-form-item[data-v-fdea79f0]:last-child{margin-bottom:10px}[data-v-fdea79f0] .ivu-table .hideRow{display:none}[data-v-fdea79f0] .vertical-center-modal{display:flex;align-items:center;justify-content:center}[data-v-fdea79f0] .vertical-center-modal .ivu-modal{top:0}",""]),e.exports=t},e31a:function(e,t,a){var r=a("24fb");t=r(!1),t.push([e.i,"#app,body,html{width:100%;height:100%}",""]),e.exports=t},fd96:function(e,t,a){"use strict";var r=a("11df"),n=a.n(r);n.a}});
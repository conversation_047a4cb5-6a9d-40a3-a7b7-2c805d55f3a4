<assembly
	xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0 http://maven.apache.org/xsd/assembly-1.1.0.xsd">
	<id>deploy</id>
	<formats>
		<format>tar.gz</format>
	</formats>
	<fileSets>
		<fileSet>  
	      <directory>${project.basedir}/lib</directory>  
	      <outputDirectory>/lib</outputDirectory>  
	    </fileSet>
		<fileSet>
			<directory>${project.basedir}/src/test/resources</directory>
			<excludes>
				<exclude>**/*.class</exclude>
				<exclude>testdata/**</exclude>
			</excludes>
			<outputDirectory>/conf</outputDirectory>
		</fileSet>
		<fileSet>
			<directory>${project.basedir}/public</directory>
			<outputDirectory>/conf/public</outputDirectory>
		</fileSet>
		<fileSet>
			<directory>${project.basedir}/src/shell</directory>
			<fileMode>0755</fileMode>
			<lineEnding>unix</lineEnding>
			<outputDirectory>/</outputDirectory>
		</fileSet>
		<fileSet>
			<directory>${project.basedir}/src/main/webapp</directory>
			<outputDirectory>/src/main/webapp</outputDirectory>
		</fileSet>
	   
		 <fileSet>
			<directory>${project.build.directory}</directory>
			<includes>
				<include>${project.artifactId}-${project.version}.jar</include>
			</includes> 
			<outputDirectory>/lib</outputDirectory>
		</fileSet> 
	</fileSets>
	
	<dependencySets>
		<dependencySet>
			<useProjectArtifact>false</useProjectArtifact>
			<outputDirectory>lib</outputDirectory><!-- 将scope为runtime的依赖包打包到lib目录下。 -->
			<scope>runtime</scope>
		</dependencySet>
	</dependencySets>
</assembly>

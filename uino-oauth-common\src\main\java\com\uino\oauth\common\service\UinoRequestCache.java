package com.uino.oauth.common.service;

import com.uino.oauth.common.bean.UinoSessionCache;
import com.uino.oauth.common.service.dao.ESUinoSessionCacheSvc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.SerializationUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.web.PortResolver;
import org.springframework.security.web.PortResolverImpl;
import org.springframework.security.web.savedrequest.DefaultSavedRequest;
import org.springframework.security.web.savedrequest.RequestCache;
import org.springframework.security.web.savedrequest.SavedRequest;
import org.springframework.security.web.util.UrlUtils;
import org.springframework.security.web.util.matcher.AnyRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.ObjectInputStream;

@Slf4j
@Component
public class UinoRequestCache implements RequestCache {

    @Autowired
    private ESUinoSessionCacheSvc esUinoSessionCacheSvc;

    private PortResolver portResolver = new PortResolverImpl();

    private boolean createSessionAllowed = true;

    private RequestMatcher requestMatcher = AnyRequestMatcher.INSTANCE;

    @Override
    public void saveRequest(HttpServletRequest request, HttpServletResponse response) {
        if (requestMatcher.matches(request)) {
            DefaultSavedRequest savedRequest = new DefaultSavedRequest(request, portResolver);
            if (createSessionAllowed || request.getSession(false) != null) {
                UinoSessionCache sessionCache = new UinoSessionCache();
                HttpSession session = request.getSession();
                String sessionId = session.getId();
                byte[] savedRequestBytes = SerializationUtils.serialize(savedRequest);
                sessionCache.setSessionId(sessionId);
                sessionCache.setSavedRequestBytes(savedRequestBytes);
                esUinoSessionCacheSvc.saveOrUpdate(sessionCache);
                log.debug("DefaultSavedRequest added to Session: " + savedRequest);
            }
        } else {
            log.debug("Request not saved as configured RequestMatcher did not match");
        }
    }

    @Override
    public SavedRequest getRequest(HttpServletRequest request, HttpServletResponse response) {
        SavedRequest saveReq = null;
        UinoSessionCache sessionCache = this.getSessionCache(request);
        if (sessionCache != null) {
            byte[] saveReqBytes = sessionCache.getSavedRequestBytes();
            ByteArrayInputStream inStream = new ByteArrayInputStream(saveReqBytes);
            ObjectInputStream oInStream = null;
            try {
                oInStream = new ObjectInputStream(inStream);
                saveReq = (DefaultSavedRequest) oInStream.readObject();
            } catch (Exception e) {
                log.error("请求缓存反序列化异常", e);
                throw new RuntimeException(e);
            } finally {
                try {
                    oInStream.close();
                } catch (IOException e) {
                }
            }
        }
        return saveReq;
    }

    @Override
    public HttpServletRequest getMatchingRequest(HttpServletRequest request, HttpServletResponse response) {
        SavedRequest saved = getRequest(request, response);
        if (!matchesSavedRequest(request, saved)) {
            log.debug("saved request doesn't match");
            return null;
        }
        removeRequest(request, response);
        return new SavedRequestAwareWrapper(saved, request);
    }

    private boolean matchesSavedRequest(HttpServletRequest request, SavedRequest savedRequest) {
        if (savedRequest == null) {
            return false;
        }
        if (savedRequest instanceof DefaultSavedRequest) {
            DefaultSavedRequest defaultSavedRequest = (DefaultSavedRequest) savedRequest;
            return defaultSavedRequest.doesRequestMatch(request, this.portResolver);
        }
        String currentUrl = UrlUtils.buildFullRequestUrl(request);
        return savedRequest.getRedirectUrl().equals(currentUrl);
    }

    @Override
    public void removeRequest(HttpServletRequest request, HttpServletResponse response) {
        UinoSessionCache sessionCache = this.getSessionCache(request);
        if(sessionCache!=null){
            log.error("read del cache[{}][{}]", sessionCache.getSessionId(),sessionCache.getSessionId());
            esUinoSessionCacheSvc.deleteById(sessionCache.getId());
            log.error("del cache sucess[{}][{}]", sessionCache.getId(),sessionCache.getSessionId());
        }
    }

    /**
     * 获取sessionCache
     *
     * @param request
     * @return
     */
    private UinoSessionCache getSessionCache(HttpServletRequest request) {
        String sessionId = null;
        if (request.getCookies() != null && request.getCookies().length > 0) {
            for (Cookie cookie : request.getCookies()) {
                if ("JSESSIONID".equals(cookie.getName().toUpperCase())) {
                    sessionId = cookie.getValue();
                }
            }
            if (sessionId == null || "".equals(sessionId)) {
                return null;
            }
            log.error("get session[{}]",sessionId);
            return esUinoSessionCacheSvc.getBySessionId(sessionId);
        } else {
            return null;
        }
    }
}

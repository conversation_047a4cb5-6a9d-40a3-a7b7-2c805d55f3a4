package com.uino.oauth.common.service;

import java.util.LinkedList;
import java.util.List;

import com.uino.api.client.permission.IOauthApiSvc;
import com.uino.bean.permission.base.OAuthClientDetail;
import com.uino.bean.permission.business.request.RegisterClientReq;
import com.uino.util.sys.BeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.uino.oauth.common.bean.UinoClientDetails;

/**
 * 客户端详情服务
 * 在Spring Security 6中不再实现ClientDetailsService和ClientRegistrationService接口
 *
 * <AUTHOR>
 */
@Service
public class UinoClientDetailsService {

    @Autowired
    private IOauthApiSvc clientSvc;

    /**
     * 通过客户端code获取客户端信息
     *
     * @param clientId 客户端ID
     * @return 客户端详情
     * @throws RuntimeException 如果客户端未注册
     */
    public UinoClientDetails loadClientByClientId(String clientId) throws RuntimeException {
        OAuthClientDetail detailInfo = clientSvc.getClientInfoByCode(clientId);
        if (detailInfo == null)
            throw new RuntimeException("客户端未注册");
        return new UinoClientDetails(detailInfo);
    }

    /**
     * 添加客户端详情
     *
     * @param clientDetails 客户端详情
     * @throws RuntimeException 如果客户端已存在
     */
    public void addClientDetails(UinoClientDetails clientDetails) throws RuntimeException {
        RegisterClientReq saveDto = ClientDetailsToSaveDto(clientDetails);
        clientSvc.register(saveDto);
    }

    /**
     * 将客户端详情转换为注册请求DTO
     *
     * @param clientDetails 客户端详情
     * @return 注册请求DTO
     */
    private RegisterClientReq ClientDetailsToSaveDto(UinoClientDetails clientDetails) {
        Assert.notNull(clientDetails, "客户端详情不能为空");
        return BeanUtil.converBean(clientDetails, RegisterClientReq.class);
    }

    /**
     * 更新客户端详情
     *
     * @param clientDetails 客户端详情
     * @throws RuntimeException 如果客户端不存在
     */
    public void updateClientDetails(UinoClientDetails clientDetails) throws RuntimeException {
        RegisterClientReq saveDto = ClientDetailsToSaveDto(clientDetails);
        clientSvc.register(saveDto);
    }

    /**
     * 更新客户端密钥
     *
     * @param clientId 客户端ID
     * @param secret 新密钥
     * @throws RuntimeException 如果客户端不存在
     */
    public void updateClientSecret(String clientId, String secret) throws RuntimeException {
        UinoClientDetails client = loadClientByClientId(clientId);
        client.getClientInfo().setClientSecret(secret);
        updateClientDetails(client);
    }

    /**
     * 移除客户端详情
     *
     * @param clientId 客户端ID
     * @throws RuntimeException 如果客户端不存在
     */
    public void removeClientDetails(String clientId) throws RuntimeException {
        clientSvc.removeClientInfoByCode(clientId);
    }

    /**
     * 列出所有客户端详情
     *
     * @return 客户端详情列表
     */
    public List<UinoClientDetails> listClientDetails() {
        List<UinoClientDetails> returnVals = new LinkedList<>();
        clientSvc.getAllClents().forEach(client -> returnVals.add(new UinoClientDetails(client)));
        return returnVals;
    }
}

package com.uino.oauth.server.init;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import lombok.extern.slf4j.Slf4j;

@ComponentScan(basePackages = { "com.uino.api.client.*.rpc", "com.uino.provider.feign" })
@Configuration
@EnableFeignClients(basePackages = { "com.uino.provider.feign" })
@ConditionalOnProperty(prefix = "base", name = "load-type", havingValue = "rpc")
@Slf4j
public class RpcRunConfig {

	{
		log.info("发现spring-boot配置为rpc加载");
	}
}

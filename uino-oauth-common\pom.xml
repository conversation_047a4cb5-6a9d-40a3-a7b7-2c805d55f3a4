<?xml version="1.0"?>
<project
		xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
		xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.uino</groupId>
		<artifactId>uino-oauth</artifactId>
		<version>EAM-2.9.1-RELEASE</version>
	</parent>
	<artifactId>uino-oauth-common</artifactId>
	<name>uino-oauth-common</name>
	<url>http://maven.apache.org</url>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>
	<dependencies>
		<dependency>
			<groupId>com.uino</groupId>
			<artifactId>uino-eam-micro-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<artifactId>commons-net</artifactId>
					<groupId>commons-net</groupId>
				</exclusion>
				<exclusion>
					<artifactId>pagehelper</artifactId>
					<groupId>com.github.pagehelper</groupId>
				</exclusion>
			</exclusions>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>org.springframework.security</groupId>-->
<!--			<artifactId>spring-security-jwt</artifactId>-->
<!--			<version>1.0.7.RELEASE</version>-->
<!--		</dependency>-->
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-oauth2-client</artifactId>
		</dependency>
	</dependencies>
</project>

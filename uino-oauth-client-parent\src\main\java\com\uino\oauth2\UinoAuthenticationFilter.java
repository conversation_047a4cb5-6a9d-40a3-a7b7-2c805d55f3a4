package com.uino.oauth2;

import com.uino.oauth.common.service.UinoTokenExtractor;
import com.uino.oauth.common.service.UinoTokenStore;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * 自定义JWT认证过滤器
 */
@Slf4j
public class UinoAuthenticationFilter extends OncePerRequestFilter {

    private final UinoTokenExtractor tokenExtractor;

    private final UinoTokenStore tokenStore;

    public UinoAuthenticationFilter(UinoTokenExtractor tokenExtractor,UinoTokenStore tokenStore) {
        this.tokenExtractor = tokenExtractor;
        this.tokenStore = tokenStore;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        try {
            // 从请求中提取token
            String token = tokenExtractor.extractToken(request);
            if (token != null) {
                log.debug("从请求中提取到token: {}", token);
                Authentication authentication = tokenStore.readAuthentication(token);
                //authentication为null就返回401
                if (authentication == null) {
                    log.debug("认证信息为空");
                    response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                    return;
                }
                SecurityContextHolder.getContext().setAuthentication(authentication);
                log.debug("设置认证信息成功");
            }
        } catch (Exception e) {
            log.error("处理token时发生异常: {}", e.getMessage(), e);
            // 不抛出异常，让请求继续处理
        }
        filterChain.doFilter(request, response);
    }
}
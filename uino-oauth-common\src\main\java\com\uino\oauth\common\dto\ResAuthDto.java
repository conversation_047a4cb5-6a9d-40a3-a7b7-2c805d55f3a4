package com.uino.oauth.common.dto;

import java.io.Serializable;
import java.util.Set;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 资源权限控制数据传输类
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResAuthDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 受保护资源匹配uri正则，支持多条件
	 */
	private Set<String> uriMatchers;

	/**
	 * 这些资源需要的授权范围
	 */
	private String scope;

	/**
	 * 访问这些匹配上的资源所需角色
	 */
	private Set<String> hasRoles;

	/**
	 * 访问这些匹配上资源所需模块权限
	 */
	private Set<String> hasModuleCodes;

}

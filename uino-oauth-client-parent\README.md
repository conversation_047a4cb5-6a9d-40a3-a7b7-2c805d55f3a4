---
# uino-oauth-client-parent(下文简称为client模板)****
---
##说明
	封装了部分初始化操作，帮助以依赖该模块方式的资源客户端快速集成。其实并不推荐使用该方案进行集成，建议自行通过oauth2.0协议进行集成保持灵活性
##使用的组件方案
###token存储:
	使用了JwtTokenStore，不去真正的维护令牌的存储，而是通过解密的方式解析令牌进行令牌读取(需要与授权server中的对称加密方案进行对应)
##集成说明-可借鉴uino-oauth-demo-client01
1、首先引用该模块
2、需扫描package:com.uino.oauth.client.parent下所有类以及子包下的类
3、通过配置完成快速集成
##配置说明-以下都需要先进行客户端注册与注册信息相对应，下文不再一一说明
###客户端id
oauth.client.id=tarsier-comm
###客户端密钥
oauth.client.secret=secret
###oauth-server获取token地址
oauth.server.token_get.url=http://localhost:9080/oauth/token
###需要oauth-server授权后code换token的回调地址
oauth.server.token_callback.url=http://localhost:1536/tarsier-comm/code2token
###资源保护json
####示例
	oauth.res.json=[{"uriMatchers":["/**"],"scope":"tarsier-comm","hasRoles":["oauth_test"],"hasModuleCodes":["07"]}]
####json格式说明
	下文将该json对象称为obj
	最外层格式为[{node01},{node02}],每一个node描述一些uri的匹配规则以及权限限定，现在对node格式进行说明
	obj[0].uriMatchers:支持多uri匹配，表示这些uri将按照这些规则进行资源保护，具体写法可参照org.springframework.security.web.util.matcher.AntPathRequestMatcher
	obj[0].scope:匹配到资源需要的授权范围，拥有才可访问（目前授权server中提供了自动授权，登陆即拥有所有授权范围，所以只要该客户端持有这些范围则可访问）
	obj[0].hasRoles:匹配到资源需要的角色，用户持有这些角色才可访问
	obj[0].hasModuleCodes:匹配到的资源需要的模块code，用户持有这些模块权限才可访问
















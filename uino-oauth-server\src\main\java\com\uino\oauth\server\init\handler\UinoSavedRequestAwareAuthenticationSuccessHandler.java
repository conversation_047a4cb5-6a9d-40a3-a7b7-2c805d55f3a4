package com.uino.oauth.server.init.handler;

import java.io.IOException;
import java.util.List;

import jakarta.annotation.PostConstruct;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.binary.core.util.BinaryUtils;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.SysUserPwdResetParam;
import com.uino.bean.permission.query.CSysUser;
import com.uino.oauth.common.service.UinoRequestCache;
import com.uino.util.sys.SysUtil;

@Component
public class UinoSavedRequestAwareAuthenticationSuccessHandler extends SavedRequestAwareAuthenticationSuccessHandler {

    @Autowired
    private IUserApiSvc userApi;

    @Value("${login.success.url}")
    private String loginSucessUrl;

    @Autowired
    private UinoRequestCache requestCache;

    @PostConstruct
    public void init() {
        super.setRequestCache(requestCache);
    }

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
            Authentication authentication) throws ServletException, IOException {
        String userName = request.getParameter("username");
		String userPasswd = request.getParameter("password");
		// 将RequestAttributes对象设置为子线程共享
		ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		String ipAddress = SysUtil.getIpAddress(sra.getRequest());
		sra.setAttribute("Authorization-Client-IP", ipAddress, 1);
		RequestContextHolder.setRequestAttributes(sra, true);
        new Thread(new Runnable() {
            @Override
			public void run() {
                if (userName.contains("+")) {
                    String[] split = userName.split("\\+");
                    userApi.recordLoginFailNum(Long.valueOf(split[0]), split[1], true);
                }else {
                    userApi.recordLoginFailNum(userName, true);
                }

				// 兼容旧版本的MD5加密，登录时更新为256Hex加密
				CSysUser cdt = new CSysUser();
				cdt.setLoginCodeEqual(userName);
				List<SysUser> users = userApi.getSysUserByCdt(cdt);
				if (!BinaryUtils.isEmpty(users) && users.get(0).getLoginPasswd().length() <= 32) {
                    SysUserPwdResetParam pwdParam = null;
                    if (userName.contains("+")) {
                        String[] split = userName.split("\\+");
                        pwdParam = SysUserPwdResetParam.builder().domainId(Long.valueOf(split[0])).loginCode(split[1])
                                .newPasswd(userPasswd).build();
                    }else {
                        pwdParam = SysUserPwdResetParam.builder().loginCode(userName)
                                .newPasswd(userPasswd).build();
                    }

					userApi.resetPwdByAdmin(pwdParam);
				}
            }
        }).start();
        Cookie cookie = new Cookie("validCode", "");
        cookie.setPath("/");
        cookie.setMaxAge(0);
        response.addCookie(cookie);
        try {
            Thread.sleep(500L);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        Cookie tryNumCookie = new Cookie("cookieTryNum", "");
        tryNumCookie.setPath("/");
        tryNumCookie.setMaxAge(0);
        response.addCookie(tryNumCookie);
        super.setDefaultTargetUrl(loginSucessUrl);
        super.setAlwaysUseDefaultTargetUrl(true);
        logger.info("==============进来了================Location:{"+loginSucessUrl+"}");
        super.onAuthenticationSuccess(request, response, authentication);
        requestCache.removeRequest(request,response);
    }

}

FROM docker.udolphin.com/java/java8:1.0
MAINTAINER uino
USER root
ADD uino-oauth-server-0.0.1-SNAPSHOT-deploy.tar /usr/local/
ADD docker_start.sh /usr/local/uino-oauth-server-0.0.1-SNAPSHOT/bin
ADD application.properties /usr/local/
RUN rm -rf /usr/local/uino-oauth-server-0.0.1-SNAPSHOT/conf/application.properties \
	&& mv /usr/local/application.properties /usr/local/uino-oauth-server-0.0.1-SNAPSHOT/conf/ \
	&& chmod -R 777 /usr/local/uino-oauth-server-0.0.1-SNAPSHOT/* 
CMD /usr/local/uino-oauth-server-0.0.1-SNAPSHOT/bin/docker_start.sh

package com.uino.oauth.server.init;


import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * OAuth2 Scope 服务
 * 用于注册和管理 OAuth2 scope
 *
 * <AUTHOR>
 */
@Component
public class OAuth2ScopeService {

    private final Set<String> registeredScopes = new HashSet<>();
    private final Map<String, String> scopeDescriptions = new HashMap<>();

    /**
     * 初始化方法，注册所有需要的 scope
     */
    @PostConstruct
    public void init() {
        // 注册标准 scope
        registerScope("openid", "OpenID Connect 认证");
        registerScope("profile", "用户个人资料");
        registerScope("email", "用户电子邮件");
        registerScope("address", "用户地址信息");
        registerScope("phone", "用户电话信息");

        // 注册自定义 scope
        registerScope("api", "API 访问权限");
        registerScope("refresh", "刷新令牌权限");
    }

    /**
     * 注册一个新的 scope
     *
     * @param scope 要注册的 scope 名称
     */
    public void registerScope(String scope) {
        registeredScopes.add(scope);
    }

    /**
     * 注册一个新的 scope 并添加描述
     *
     * @param scope 要注册的 scope 名称
     * @param description scope 的描述
     */
    public void registerScope(String scope, String description) {
        registeredScopes.add(scope);
        scopeDescriptions.put(scope, description);
    }

    /**
     * 检查 scope 是否已注册
     *
     * @param scope 要检查的 scope 名称
     * @return 如果已注册返回 true，否则返回 false
     */
    public boolean isScopeRegistered(String scope) {
        return registeredScopes.contains(scope);
    }

    /**
     * 获取所有已注册的 scope
     *
     * @return 已注册的 scope 集合
     */
    public Set<String> getRegisteredScopes() {
        return new HashSet<>(registeredScopes);
    }

    /**
     * 获取 scope 的描述
     *
     * @param scope 要获取描述的 scope 名称
     * @return scope 的描述，如果没有描述则返回 scope 名称
     */
    public String getScopeDescription(String scope) {
        return scopeDescriptions.getOrDefault(scope, scope);
    }

    /**
     * 获取所有 scope 的描述
     *
     * @return scope 名称到描述的映射
     */
    public Map<String, String> getScopeDescriptions() {
        return new HashMap<>(scopeDescriptions);
    }
}

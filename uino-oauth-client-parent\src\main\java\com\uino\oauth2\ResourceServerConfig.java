package com.uino.oauth2;

import com.uino.api.client.permission.IOauthApiSvc;
import com.uino.bean.permission.base.OauthResourceDetail;
import com.uino.oauth.common.service.UinoTokenExtractor;
import com.uino.oauth.common.service.UinoTokenStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.util.matcher.RequestHeaderRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Configuration
@EnableMethodSecurity(prePostEnabled = true, securedEnabled = true)
@Slf4j
public class ResourceServerConfig {

    @Autowired
    private UinoTokenStore tokenStore;

    @Autowired
    private UinoTokenExtractor tokenExtractor;

    @Autowired
    private IOauthApiSvc oauthApiSvc;

    @Value("${oauth.client.id}")
    private String clientId;

    @Value("${oauth.open:false}")
    private boolean needAuth;

    @Value("${server.servlet.context-path:/}")
    private String contextPath;

    @Bean
    @ConditionalOnMissingBean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    @Bean
    @Qualifier("authorizationHeaderRequestMatcher")
    public RequestMatcher authorizationHeaderRequestMatcher() {
        return new RequestHeaderRequestMatcher("Authorization");
    }

    @Bean
    public SecurityFilterChain resourceServerSecurityFilterChain(HttpSecurity http) throws Exception {
        SimpleAuthenticationEntryPoint authenticationEntryPoint = new SimpleAuthenticationEntryPoint();
        authenticationEntryPoint.setRedirectAuthPrefix(contextPath);

        http
            .csrf(csrf -> csrf.disable())
            .cors(cors -> cors.disable())
            .exceptionHandling(exceptions -> exceptions
                .authenticationEntryPoint(authenticationEntryPoint)
            )
            .headers(headers -> headers
                .frameOptions(frame -> frame.disable())
            )
            .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            );

        if (needAuth) {
            OauthResourceDetail resource = oauthApiSvc.getResourceDetail(clientId);
            // 创建自定义JWT认证过滤器
            UinoAuthenticationFilter uinoAuthenticationFilter = new UinoAuthenticationFilter(tokenExtractor,tokenStore);
            // 添加自定义JWT认证过滤器
            http.addFilterBefore(uinoAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

            if (resource != null) {
                final List<String> permitAllUrls = resource.getPermitAllUrls();
                permitAllUrls.add("/webjars/css/*.css");
                permitAllUrls.add("/webjars/js/*.js");
                permitAllUrls.add("/swagger-resources");
                permitAllUrls.add("/v2/api-docs");
                permitAllUrls.add("/doc.html");

                http.authorizeHttpRequests(authorize -> {
                    // 允许公共URL访问
                    authorize.requestMatchers(permitAllUrls.toArray(new String[0])).permitAll();

                    // 配置URL安全规则
                    List<OauthResourceDetail.UrlSecureConfig> urlConfigs = resource.getUrlSecureConfigs();
                    if (urlConfigs != null && !urlConfigs.isEmpty()) {
                        for (OauthResourceDetail.UrlSecureConfig urlConfig : urlConfigs) {
                            String access = urlConfig.buildAccess();
                            String[] urls = urlConfig.getUrls().toArray(new String[0]);

                            if (access == null || "".equals(access)) {
                                authorize.requestMatchers(urls).authenticated();
                            } else {
                                // 在Spring Security 6中，不再支持直接使用access()方法
                                // 这里需要根据access的内容创建适当的规则
                                authorize.requestMatchers(urls).authenticated();
                            }
                        }
                    }

                    // 默认规则
                    authorize.anyRequest().authenticated();
                });
            } else {
                log.warn("未在资源信息中寻找到【{}】资源配置，使用默认资源保护配置", clientId);
                http.authorizeHttpRequests(authorize -> authorize
                    .requestMatchers("/redirectAuth", "/getTokenByCode", "/refreshToken",
                            "/webjars/css/*.css", "/webjars/js/*.js", "/swagger-resources", "/v2/api-docs", "/doc.html")
                    .permitAll()
                    .anyRequest().authenticated()
                );
            }
        } else {
            http.authorizeHttpRequests(authorize -> authorize
                .anyRequest().permitAll()
            );
        }
        return http.build();
    }


}

package com.uino.oauth.server;

import com.uino.util.sys.LdapUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;


import lombok.extern.slf4j.Slf4j;

/**
 * 启动权限认证server
 * 
 * <AUTHOR>
 *
 */
@SpringBootApplication(exclude = {QuartzAutoConfiguration.class})
@Configuration
@ServletComponentScan
@ComponentScans(value = {@ComponentScan(basePackages = {
        "com.uino.oauth.common",
        "com.uino.oauth.server.init",
        "com.uino.oauth.server.web"})})
@Slf4j
public class StartAuthServer {

    public static void main(String[] args) {
        LdapUtil.initLdapsConfig();
        SpringApplication.run(StartAuthServer.class, args);
    }

}

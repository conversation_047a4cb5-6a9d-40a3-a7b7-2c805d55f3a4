package com.uino.oauth.server.init.handler;

import java.io.IOException;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.access.AccessDeniedHandlerImpl;
import org.springframework.stereotype.Component;

import com.binary.framework.util.ControllerUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 自定义403无权限处理策略
 * <p>
 * 判断返回结果，ajax请求的话返回403状态嘛并返回json相应信息；如果为其他则按照默认结果处理
 *
 * <AUTHOR>
 *
 */
@Component
@Slf4j
public class UinoAccessDeniedHandler implements AccessDeniedHandler {
	// spring security的默认无权限处理策略，直接跳转403
	private AccessDeniedHandlerImpl defaultAccessDeniedHandler;
	// 自定义403跳转地址
	@Value("${error.403.url:}")
	private String error403Page;

	@Override
	public void handle(HttpServletRequest request, HttpServletResponse response,
			AccessDeniedException accessDeniedException) throws IOException, ServletException {
		if (ajaxRequest(request, response)) {
			response.setStatus(HttpStatus.FORBIDDEN.value());
			ControllerUtils.returnJson(request, response, "用户/客户端无该权限");
		} else {
			this.getDefaultAccessDeniedHandler().handle(request, response, accessDeniedException);
		}
	}

	/**
	 * 判断是否为ajax请求
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	private boolean ajaxRequest(HttpServletRequest request, HttpServletResponse response) {
		String xReqWith = request.getHeader("X-Requested-With");
		if (xReqWith == null || "".equals(xReqWith)) {
			return false;
		} else if ("XMLHttpRequest".equalsIgnoreCase(xReqWith)) {
			return true;
		} else {
			log.error("发现异常未知X-Requested-With【{}】,按照默认处理无权限请求", xReqWith);
			return false;
		}
	}

	/**
	 * 获取默认的403处理策略
	 *
	 * @return
	 */
	private AccessDeniedHandlerImpl getDefaultAccessDeniedHandler() {
		if (this.defaultAccessDeniedHandler == null) {
			synchronized (UinoAccessDeniedHandler.class) {
				if (this.defaultAccessDeniedHandler == null) {
					this.defaultAccessDeniedHandler = new AccessDeniedHandlerImpl();
					if (this.error403Page != null && !"".equals(this.error403Page.trim()))
						this.defaultAccessDeniedHandler.setErrorPage(this.error403Page);
				}
			}
		}
		return this.defaultAccessDeniedHandler;
	}

}

package com.uino.oauth.server.web;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Properties;

import javax.imageio.ImageIO;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.permission.business.CurrentUserInfo;
import com.uino.bean.permission.business.UserInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.google.code.kaptcha.util.Config;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
public class UserController {
	@Autowired
	private IUserApiSvc userApi;
	@RequestMapping("/user/getValidCodeImg")
	public ResponseEntity<byte[]> getValidCodeImg(HttpServletResponse response) throws IOException {
		DefaultKaptcha defaultKaptcha = new DefaultKaptcha();
		Properties properties = new Properties();
		// 图片边框
		properties.setProperty("kaptcha.border", "yes");
		// 边框颜色
		properties.setProperty("kaptcha.border.color", "105,179,90");
		// 字体颜色
		properties.setProperty("kaptcha.textproducer.font.color", "red");
		// 图片宽
		properties.setProperty("kaptcha.image.width", "110");
		// 图片高
		properties.setProperty("kaptcha.image.height", "40");
		// 字体大小
		properties.setProperty("kaptcha.textproducer.font.size", "30");
		// session key
		properties.setProperty("kaptcha.session.key", "validCode");
		// 验证码长度
		properties.setProperty("kaptcha.textproducer.char.length", "4");
		// 字体
		properties.setProperty("kaptcha.textproducer.font.names", "宋体,楷体,微软雅黑");
		Config config = new Config(properties);
		defaultKaptcha.setConfig(config);

		// 生产验证码字符串
		String code = defaultKaptcha.createText();
		// 使用生产的验证码字符串返回一个BufferedImage对象
		BufferedImage image = defaultKaptcha.createImage(code);
		Cookie cookie = new Cookie("validCode", code);
		cookie.setPath("/");
		response.addCookie(cookie);
		ByteArrayOutputStream os = new ByteArrayOutputStream();
		ImageIO.write(image, "png", os);
		byte[] res = os.toByteArray();
		os.close();
		HttpHeaders header = new HttpHeaders();
		header.setContentType(MediaType.IMAGE_PNG);
		ResponseEntity<byte[]> rep = new ResponseEntity<byte[]>(res, header, HttpStatus.OK);
		return rep;
	}
	@RequestMapping("/user/getCurrentUser")
	@ResponseBody
	public UserInfo getUser(){
		CurrentUserInfo currentUser = userApi.getCurrentUser();
		Long userId = currentUser.getId();
		return userApi.getUserInfoById(userId);
	}
}

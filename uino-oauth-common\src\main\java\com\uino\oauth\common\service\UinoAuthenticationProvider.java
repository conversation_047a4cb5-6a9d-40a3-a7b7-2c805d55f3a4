package com.uino.oauth.common.service;

import com.uino.util.sys.SysUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;



/**
 * 偷懒直接继承了
 * {@code org.springframework.security.authentication.dao.DaoAuthenticationProvider}
 * 
 * <AUTHOR>
 *
 */
public class UinoAuthenticationProvider extends DaoAuthenticationProvider {

    private final static String ErrorPass = "AAAAAAA";

    @Autowired
    UinoLdapAuthenticationProvider ldapAuthenticationProvider;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        long startTime = System.currentTimeMillis();
        UserDetails user = loadUserByUsername(authentication);
        boolean succeed = false;
        // 新增ldap验证
        if (ldapAuthenticationProvider.isActive()) {
            succeed = ldapAuthenticationProvider.authenticate(this.getUserDetailsService(), user, authentication);
            if (logger.isDebugEnabled()) {
                logger.debug("ldap 验证耗时[" + (System.currentTimeMillis() - startTime) + "],验证[" + (succeed ? "通过" : "失败")
                        + "]");
            }
        }
        if (succeed && user == null) {
            // 用户是新建的情况重新加载用户信息,
            // 并且判断这个用户是否存在.GDDDDD
            user = retrieveUser2(authentication);
        } else if (!succeed && user == null) {
            throwUserNotFound(authentication);
        }
        getPreAuthenticationChecks().check(user);
        if (!succeed) {
            additionalAuthenticationChecks(user, (UsernamePasswordAuthenticationToken) authentication);
        }
        getPostAuthenticationChecks().check(user);
        Object principalToReturn = user;
        if (isForcePrincipalAsString()) {
            principalToReturn = user.getUsername();
        }
        return createSuccessAuthentication(principalToReturn, authentication, user);
    }

    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails,
            UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
        if (authentication.getCredentials() == null) {
            logger.debug("Authentication failed: no credentials provided");
            throw new BadCredentialsException(
                    messages.getMessage("AbstractUserDetailsAuthenticationProvider.badCredentials", "Bad credentials"));
        }
        String presentedPassword = authentication.getCredentials().toString();
        presentedPassword = SysUtil.EncryptDES.decryptDES(presentedPassword);
        if (!getPasswordEncoder().matches(presentedPassword, userDetails.getPassword())) {
            logger.debug("Authentication failed: password does not match stored value");
            throw new BadCredentialsException(
                    messages.getMessage("AbstractUserDetailsAuthenticationProvider.badCredentials", "Bad credentials"));
        }
    }

    protected void throwUserNotFound(Authentication authentication) {
        if (authentication.getCredentials() != null) {
            // 减缓攻击???
            String presentedPassword = authentication.getCredentials().toString();
            this.getPasswordEncoder().matches(presentedPassword, ErrorPass);
        }
        throw new UsernameNotFoundException("用户【" + authentication.getName() + "】不存在");
    }

    /**
     * 模仿父类写法.
     * 
     * @param authentication
     * @return
     */
    protected UserDetails retrieveUser2(Authentication authentication) {
        try {
            return retrieveUser(authentication.getName(), (UsernamePasswordAuthenticationToken) authentication);
        } catch (UsernameNotFoundException notFound) {
            logger.debug("User '" + authentication.getName() + "' not found");
            if (hideUserNotFoundExceptions) {
                throw new BadCredentialsException(messages
                        .getMessage("AbstractUserDetailsAuthenticationProvider.badCredentials", "Bad credentials"));
            } else {
                throw notFound;
            }
        }
    }

    protected UserDetails loadUserByUsername(Authentication authentication) {
        try {
            return this.getUserDetailsService().loadUserByUsername(authentication.getName());
        } catch (UsernameNotFoundException e) {
            return null;
        }
    }
}

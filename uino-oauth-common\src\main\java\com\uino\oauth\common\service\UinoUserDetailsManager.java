package com.uino.oauth.common.service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.query.CSysUser;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.provisioning.UserDetailsManager;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.uino.oauth.common.bean.UinoUserDetails;

import lombok.extern.slf4j.Slf4j;

/**
 * 实现获取用户服务接口-UserDetailsManager继承了UserDetailsService，并持有更多功能，故选择实现该接口
 * 
 * <AUTHOR>
 *
 */
@Slf4j
@Service
public class UinoUserDetailsManager implements UserDetailsManager {
	@Autowired
	private IUserApiSvc userApiSvc;

	/**
	 * 根据用户名（登陆名）获取用户，不存在抛出UsernameNotFoundException
	 */
	@Override
	public UinoUserDetails loadUserByUsername(String username) throws UsernameNotFoundException {

		CSysUser userQuery = new CSysUser();
		if (username.contains("+")) {
			String[] split = username.split("\\+");
			userQuery.setDomainId(Long.valueOf(split[0]));
			userQuery.setLoginCodeEqual(split[1]);
		}else {
			userQuery.setLoginCodeEqual(username);
		}
		List<UserInfo> users = userApiSvc.getUserInfoByCdt(userQuery, false);
		if (users == null || users.size() < 1) {
			throw new UsernameNotFoundException("用户【" + username + "】不存在");
		} else if (users.size() > 1) {
			log.error("检索到多个登陆名为【{}】的用户，本次使用id为【{}】的用户", username, users.get(0).getId());
		}
		Set<String> moduleCodes = new HashSet<>();
		UserInfo user = users.get(0);
		ModuleNodeInfo moduleTree = userApiSvc.getModuleTree(user.getId());
		this.fillModuleAuth(moduleTree, moduleCodes);
		return new UinoUserDetails(user, moduleCodes);
	}

	private void fillModuleAuth(ModuleNodeInfo node, Set<String> moduleCodes) {
		if (node.getModuleCode() != null && !node.getModuleCode().trim().equals("")) {
			moduleCodes.add(node.getModuleCode());
		}
		if (node.getChildren() != null) {
			for (ModuleNodeInfo next : node.getChildren()) {
				fillModuleAuth(next, moduleCodes);
			}
		}
	}

	/**
	 * 创建用户，不提供创建用户定制密码功能
	 */
	@Override
	public void createUser(UserDetails user) {
		// Assert.isTrue(false, "暂不提供创建用户功能");
		UserInfo saveDto = new UserInfo();
		saveDto.setLoginCode(user.getUsername());
		saveDto.setUserName(user.getUsername());
		Long res = userApiSvc.saveOrUpdate(saveDto);
		if (res == null || res.longValue() <= 0) {
			throw new RuntimeException("创建用户失败");
		}
	}

	/**
	 * 修改用户信息,暂不提供
	 */
	@Override
	public void updateUser(UserDetails user) {
		Assert.isTrue(false, "暂不提供修改用户信息功能");
	}

	/**
	 * 删除用户
	 */
	@Override
	public void deleteUser(String username) {
		// TODO Auto-generated method stub
		Assert.isTrue(false, "暂不提供删除用户功能");
	}

	/**
	 * 修改当前用户密码
	 */
	@Override
	public void changePassword(String oldPassword, String newPassword) {
		// 当前用户信息
		Authentication currentUser = SecurityContextHolder.getContext().getAuthentication();
		if (currentUser == null) {
			// This would indicate bad coding somewhere
			throw new AccessDeniedException(
					"Can't change password as no Authentication object found in context " + "for current user.");
		}
		String loginCode = currentUser.getName();
		log.debug("Changing password for user '{}'", loginCode);
	}

	/**
	 * 校验指定登陆名用户是否存在
	 */
	@Override
	public boolean userExists(String username) {
		long res = userApiSvc.countByCondition(QueryBuilders.termQuery("loginCode.keyword", username));
		return res > 0;
	}

}

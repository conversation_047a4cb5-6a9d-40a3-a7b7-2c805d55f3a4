package com.uino.oauth.server.init;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.cache.NullUserCache;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import com.uino.oauth.common.service.UinoAuthenticationProvider;
import com.uino.oauth.common.service.UinoPasswordEncoder;
import com.uino.oauth.common.service.UinoRequestCache;
import com.uino.oauth.common.service.UinoUserDetailsManager;
import com.uino.oauth.server.init.handler.UinoAccessDeniedHandler;
import com.uino.oauth.server.init.handler.UinoAuthenticationFailureHandler;
import com.uino.oauth.server.init.handler.UinoLogoutHandler;
import com.uino.oauth.server.init.handler.UinoLogoutSucessHandler;
import com.uino.oauth.server.init.handler.UinoSavedRequestAwareAuthenticationSuccessHandler;

import lombok.extern.slf4j.Slf4j;

@EnableMethodSecurity(prePostEnabled = true)
@EnableWebSecurity
@Configuration
@Order(90)
@Slf4j
public class SecurityConfiguration {

    @Autowired
    private UinoUserDetailsManager userDetailsService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private ValidCodeFilter validCodeFilter;

    @Autowired
    private UinoAuthenticationFailureHandler uinoAuthenticationFailureHandler;

    @Autowired
    private UinoLogoutHandler uinoLogoutHandler;

    @Autowired
    private UinoLogoutSucessHandler uinoLogoutSucessHandler;

    @Autowired
    private UinoSavedRequestAwareAuthenticationSuccessHandler uinoSavedRequestAwareAuthenticationSuccessHandler;

    @Autowired
    private UinoAccessDeniedHandler uinoAccessDeniedHandler;

    @Autowired
    private UinoRequestCache uinoRequestCache;

    @Value("${login.page.url:/index.html}")
    private String loginPageUrl;

    @Bean
    public UserDetailsService userDetailsService() {
        return userDetailsService;
    }

    @Bean
    public AuthenticationManager authenticationManager(HttpSecurity http) throws Exception {
        return http.getSharedObject(AuthenticationManagerBuilder.class)
                .authenticationProvider(authenticationProvider())
                .build();
    }

    @Bean
    public AuthenticationManager authenticationManagerBean(HttpSecurity http) throws Exception {
        return authenticationManager(http);
    }

    @Bean
    public UinoAuthenticationProvider authenticationProvider() {
        UinoAuthenticationProvider authProvider = new UinoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService);
        authProvider.setPasswordEncoder(passwordEncoder);
        // 由于缓存会涉及到刷新移除等问题，暂不使用缓存
        authProvider.setUserCache(new NullUserCache());
        return authProvider;
    }

    /**
     * 密码加密
     *
     * @return
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new UinoPasswordEncoder();
        // return NoOpPasswordEncoder.getInstance();
    }

    @Bean
    public SecurityFilterChain defaultSecurityFilterChain(HttpSecurity http) throws Exception {
        loginPageUrl = loginPageUrl != null && !"".equals(loginPageUrl) ? loginPageUrl : "index.html";
        log.info("登录页地址为【{}】", loginPageUrl);

        http
            .formLogin(form -> form
                .loginPage(loginPageUrl)
                .loginProcessingUrl("/login")
                .failureHandler(uinoAuthenticationFailureHandler)
                .successHandler(uinoSavedRequestAwareAuthenticationSuccessHandler)
            )
            .csrf(csrf -> csrf.disable())
            .addFilterBefore(validCodeFilter, UsernamePasswordAuthenticationFilter.class)
            .logout(logout -> logout
                .logoutSuccessHandler(uinoLogoutSucessHandler)
                .addLogoutHandler(uinoLogoutHandler)
                .invalidateHttpSession(true)
                .clearAuthentication(true)
            )
            .exceptionHandling(exceptions -> exceptions
                .accessDeniedHandler(uinoAccessDeniedHandler)
            )
            .requestCache(cache -> cache
                .requestCache(uinoRequestCache)
            )
            .cors(cors -> cors.disable())
            .authorizeHttpRequests(authorize -> authorize
                .requestMatchers("/login", "/view/**", "/lib/**", "/js/**", "/css/**", "/img/**",
                        "/fonts/**", "/favicon.ico", "/index.html", "/oauth2/**","/user/getValidCodeImg")
                .permitAll()
                .anyRequest().authenticated()
            );

        return http.build();
    }
}

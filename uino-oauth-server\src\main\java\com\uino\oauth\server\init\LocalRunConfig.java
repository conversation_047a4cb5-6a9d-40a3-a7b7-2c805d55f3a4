package com.uino.oauth.server.init;

import java.util.LinkedHashMap;
import java.util.Map;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.binary.core.i18n.Language;
import com.uinnova.product.devcloud.i18n.client.trans.LanguageGetter;

import lombok.extern.slf4j.Slf4j;

@Configuration
@ComponentScan(basePackages = {
		"com.uino.bean",
		"com.uino.service",
		"com.uino.monitor",
		"com.uino.dao",
		"com.uino.util",
		"com.uino.api.client.*.local"})
@ConditionalOnProperty(prefix = "base", name = "load-type", havingValue = "local", matchIfMissing = true)
@Slf4j
public class LocalRunConfig {

	{
		log.info("发现spring-boot配置为本地加载");
	}
	@Bean
	@ConditionalOnMissingBean
	public LanguageGetter languageGetter() {
		return () -> {
			try {
				ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
				String Language = attrs.getRequest().getLocale().getLanguage();
				log.info("语言获取器注册成功");
				return httpHeaderLanguageDict.get(Language);
			} catch (Exception e) {
				log.debug("获取语言失败");
			}
			return null;
		};
	}
	private static final Map<String, Language> httpHeaderLanguageDict = new LinkedHashMap<>();
	static {
		httpHeaderLanguageDict.put("zh-cn", Language.ZHC);
		httpHeaderLanguageDict.put("zh", Language.ZHC);
		httpHeaderLanguageDict.put("en-US", Language.EN);
		httpHeaderLanguageDict.put("en", Language.EN);
	}


}

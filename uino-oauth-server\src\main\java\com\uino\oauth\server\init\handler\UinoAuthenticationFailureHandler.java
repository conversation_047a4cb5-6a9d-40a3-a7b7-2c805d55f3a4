package com.uino.oauth.server.init.handler;

import java.io.IOException;
import java.util.List;

import javax.servlet.ServletException;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.query.CSysUser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.DefaultRedirectStrategy;
import org.springframework.security.web.RedirectStrategy;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import com.uino.oauth.common.exception.UserFormatException;
import com.uino.oauth.server.init.ValidCodeFilter.ValidCodeException;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class UinoAuthenticationFailureHandler implements AuthenticationFailureHandler {

    @Autowired
    private IUserApiSvc userApi;

    @Value("${login.page.url:/index.html}")
    private String loginPageUrl;

    private RedirectStrategy redirectStrategy = new DefaultRedirectStrategy();

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
            AuthenticationException exception) throws IOException {
        String userName = request.getParameter("username");
        String domainId = request.getParameter("domainId");
        if (domainId != null && userName.contains("\\+")) {
            userName = userName.split("\\+")[1];
        }
        // 同用户尝试失败次数
        int tryNum = 0;
        // 当前cookie尝试失败次数
        int cookieTryNum = 0;
        // 还可尝试次数
        int allowTryNum = 0;
        try {
            if (request.getCookies() != null) {
                for (Cookie cookie : request.getCookies()) {
                    if (cookie.getName().equalsIgnoreCase("cookieTryNum")) {
                        String cookieTryNumVal = cookie.getValue();
                        if (StringUtils.isNotBlank(cookieTryNumVal))
                            cookieTryNum = Integer.valueOf(cookieTryNumVal);
                    }
                }
            }
            cookieTryNum++;
        } catch (Exception e) {
            // TODO: handle exception
        }
        int errorType;
        // LockedException
        if (exception instanceof UserFormatException) {
            errorType = 4;
        }
        if (exception instanceof ValidCodeException) {
            // 验证码输入错误
            errorType = 2;
        } else if (exception instanceof LockedException) {
            // 用户状态被锁定
            errorType = 3;
        } else {
            if (domainId == null) {
                tryNum = userApi.recordLoginFailNum(userName, false);
            }else {
                tryNum = userApi.recordLoginFailNum(Long.valueOf(domainId), userName, false);
            }

            int maxTryNum = userApi.getLoginFailLockNum();
            allowTryNum = maxTryNum - tryNum;
            allowTryNum = allowTryNum < 0 ? 0 : allowTryNum;
            // 其他的就直接简单的认为用户名密码错误，其实还有好多，暂时触发不到
            errorType = 1;
        }
        /// 判定用户是否已被锁定，被锁定则提示距离解锁时间
        CSysUser userQuery = new CSysUser();
        userQuery.setLoginCodeEqual(userName);
        if (domainId != null) {
            userQuery.setDomainId(Long.valueOf(domainId));
        }
        List<UserInfo> users = userApi.getUserInfoByCdt(userQuery, false);
        long unLockDuration = 0L;
        if (users != null && users.size() > 0) {
            UserInfo user = users.get(0);
            boolean userLock = user.getLockFlag() != null && user.getLockFlag().intValue() == 1;
            if (userLock) {
                // 被锁定时间
                Long lockedTime = user.getLockedTime();
                // 需要锁定时长
                Long userLockDuration = userApi.getUserLockDuration();
                // 可解锁时间
                long unLockTime = lockedTime + userLockDuration;
                // 与当前时间比较获取N毫秒后解锁
                unLockDuration = unLockTime - System.currentTimeMillis();
                unLockDuration = unLockDuration < 0 ? 0 : unLockDuration;
                errorType = 3;
            }
        }
        Cookie tryNumCookie = new Cookie("cookieTryNum", String.valueOf(cookieTryNum));
        tryNumCookie.setPath("/");
        response.addCookie(tryNumCookie);
        // error 0:用户名密码等基础错误1:验证码错误 valid 0:还不需要验证码1:需要验证码
        String redirePage = this.loginPageUrl + "?error=" + errorType + "&valid="
                + ((tryNum >= 3) || (cookieTryNum >= 3) ? 1 : 0) + "&allowNum=" + allowTryNum + "&unLockDuration="
                + unLockDuration;
        String failMsg = exception.getMessage();
        response.addHeader("errorMsg", failMsg);
        redirectStrategy.sendRedirect(request, response, redirePage);
    }
}

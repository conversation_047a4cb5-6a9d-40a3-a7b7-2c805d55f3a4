package com.uino.oauth2;

import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.UUID;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.api.client.permission.IOauthApiSvc;
import com.uino.bean.permission.base.OauthTokenDetail;
import jakarta.servlet.http.HttpSession;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.util.Assert;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.framework.web.RemoteResult;
import com.uino.oauth.common.bean.UinoClientDetails;
import com.uino.oauth.common.service.UinoClientDetailsService;
import com.uino.oauth.common.service.UinoTokenStore;

import lombok.extern.slf4j.Slf4j;

@RestController
@Slf4j
public class ResController {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private UinoTokenStore uinoTokenStore;

    @Autowired
    private UinoClientDetailsService uinoClientSvc;

    @Autowired
    private IOauthApiSvc oauthApiSvc;

    @Value("${oauth.client.id}")
    private String clientId;

    @Value("${oauth.client.secret}")
    private String secret;

    @Value("${oauth.server.url}")
    private String oauthServerUrl;

    @Value("${oauth.server.in_url:}")
    private String oauthServerInUrl;

    @Value("${oauth.server.token_callback.url}")
    private String oauthServerCallBackTokenUrl;

    @Value("${oauth.client.grant_type}")
    private String grantType;

    @Value("${oauth.token.random_param:true}")
    private boolean buildRandomParam;

    /**
     * 获取oauth server 获取token地址
     *
     * @return
     */
    private String oauthServerGetTokenUrl() {
        return oauthServerUrl + "/oauth2/token";
    }

    /**
     * 获取内网oauth server 获取token地址
     *
     * @return
     */
    private String inOauthServerGetTokenUrl() {
        return getOauthServerInUrl() + "/oauth2/token";
    }

    /**
     * 获取oauth内网地址
     *
     * @return
     */
    private String getOauthServerInUrl() {
        return oauthServerInUrl != null && !"".equals(oauthServerInUrl) ? oauthServerInUrl : oauthServerUrl;
    }

    @RequestMapping("redirectAuth")
    public ModelAndView redirectAuth(@RequestParam(required = false) String userName,
                                     @RequestParam(required = false) String password,
                                     @RequestParam(required = false, value = "goPageUrl") String goPageUrl,
                                     HttpServletRequest request, HttpServletResponse response)
            throws UnsupportedEncodingException {
        String goUrl = null;
        HttpSession session = request.getSession();
        session.setAttribute("goPageUrl",goPageUrl);
        // 授权码模式
        if ("authorization_code".equals(grantType)) {
            if (goPageUrl != null)
                goPageUrl = URLEncoder.encode(goPageUrl, "UTF-8");
            String oauthServerRoot = this.oauthServerUrl;
            String clientId = this.clientId;
            String responseType = "code";
            String redirectUri = oauthServerCallBackTokenUrl;
            String url = oauthServerRoot + "/oauth2/authorize?client_id=" + clientId + "&response_type=" + responseType
                    + "&redirect_uri=" + redirectUri + "&goPageUrl=" + goPageUrl + "&t=" + UUID.randomUUID().toString();
            goUrl = url;
        } else if ("client_credentials".equals(grantType)) {
            // 客户端模式
            HttpHeaders headers = new HttpHeaders();
            headers.set(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("grant_type", grantType);
            params.add("client_id", clientId);
            params.add("client_secret", secret);
            params.add("t", UUID.randomUUID().toString());
            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(params, headers);
            ResponseEntity<String> rep = restTemplate.postForEntity(inOauthServerGetTokenUrl(), requestEntity,
                    String.class);
            String tokenInfoJson = rep.getBody();
            return redirectClientIndexPage(goPageUrl, tokenInfoJson, response);
        } else if ("password".equals(grantType)) {
            // 用户名密码模式
            HttpHeaders headers = new HttpHeaders();
            headers.set(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("grant_type", grantType);
            params.add("client_id", clientId);
            params.add("client_secret", secret);
            params.add("username", userName);
            params.add("password", password);
            params.add("t", UUID.randomUUID().toString());
            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(params, headers);
            ResponseEntity<String> rep = restTemplate.postForEntity(inOauthServerGetTokenUrl(), requestEntity,
                    String.class);
            String tokenInfoJson = rep.getBody();
            return redirectClientIndexPage(goPageUrl, tokenInfoJson, response);
        } else {
            Assert.isTrue(false, "暂只支持[授权码模式/客户端模式/用户名密码模式]");
        }
        return new ModelAndView("redirect:" + goUrl);
    }

    @RequestMapping("getTokenByCode")
    public ModelAndView getTokenByCode(@RequestParam String code, HttpServletRequest request,HttpServletResponse response) {
        log.info("授权成功开始code换token");
        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("grant_type", grantType);
        params.add("client_id", clientId);
        params.add("client_secret", secret);
        params.add("code", code);
        params.add("redirect_uri", oauthServerCallBackTokenUrl);
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(params, headers);
        ResponseEntity<String> rep = restTemplate.postForEntity(inOauthServerGetTokenUrl(), requestEntity,
                String.class);
        String tokenInfoJson = rep.getBody();
        JSONObject tokenInfo = JSON.parseObject(tokenInfoJson);
        String tokenCode = tokenInfo.getString("access_token");
        Authentication tokenAuth = uinoTokenStore.readAuthentication(tokenCode);
        // 在Spring Security 6中，不再使用OAuth2Authentication和OAuth2Request
        // 尝试从认证对象中获取goPageUrl
        String goPageUrl = null;
        HttpSession session = request.getSession();
        Object attribute = session.getAttribute("goPageUrl");
        if (attribute != null) {
            goPageUrl = (String) attribute;
            session.removeAttribute("goPageUrl");
        }
        session.invalidate();
        log.info("code换token结束");
        return redirectClientIndexPage(goPageUrl, tokenInfoJson, response);
    }

    private ModelAndView redirectClientIndexPage(String goPageUrl, String tokenInfoJson, HttpServletResponse response) {
        JSONObject tokenInfo = JSON.parseObject(tokenInfoJson);
        // response.addCookie(new Cookie("oauth_access_token",
        // tokenInfo.getString("access_token")));
        // response.addCookie(new Cookie("oauth_refresh_token",
        // tokenInfo.getString("refresh_token")));
        // response.addCookie(new Cookie("oauth_access_token_expiresin",
        // tokenInfo.getString("expires_in") + "s"));
        ModelAndView modelAndView = new ModelAndView();
        String oauthUrl = "";
        String param = buildRandomParam
                ? "&" + RandomStringUtils.randomAlphanumeric(3) + "=" + RandomStringUtils.randomAlphanumeric(10) : "";
        if (goPageUrl == null || "".equals(goPageUrl.trim())
                || (!goPageUrl.trim().startsWith("http") && !goPageUrl.trim().startsWith("https"))) {
            //try {
            //    goPageUrl = URLDecoder.decode(goPageUrl, "UTF-8");
            //} catch (UnsupportedEncodingException e) {
            //    Assert.isTrue(false, "url解码异常" + goPageUrl);
            //}
            UinoClientDetails clientInfo = uinoClientSvc.loadClientByClientId(clientId);
            String clientIndexPage = clientInfo.getClientIndexPage();
            oauthUrl = clientIndexPage + (clientIndexPage.indexOf("?") < 0 ? "?" : "&") + "tk="
                    + tokenInfo.getString("access_token") + "&rtk=" + tokenInfo.getString("refresh_token") + param;
        } else {
            oauthUrl = goPageUrl + (goPageUrl.indexOf("?") < 0 ? "?" : "&") + "tk="
                    + tokenInfo.getString("access_token") + "&rtk=" + tokenInfo.getString("refresh_token") + param;
        }
        modelAndView.setView(new RedirectView(oauthUrl, true, false));
        return modelAndView;
    }

    @RequestMapping("refreshToken")
    public ResponseEntity<?> refreshToken(@RequestBody String refreshTokenCode) {
        // 尝试获取刷新token对应还有效token，获取到则直接返回还有效token，获取不到则直接换取token
        OauthTokenDetail accessTokenDetail = oauthApiSvc.getTokenDetailByReTokenCode(extractTokenKey(refreshTokenCode));
        if (accessTokenDetail != null) {
            Jwt accessToken = uinoTokenStore.readAccessToken(accessTokenDetail.getTokenCode());
            // 在Spring Security 6中，JWT没有isExpired方法，需要检查过期时间
            if (accessToken != null && !isTokenExpired(accessToken)) {
                JSONObject repDto = new JSONObject();
                repDto.put("access_token", accessTokenDetail.getTokenCode());
                RemoteResult res = new RemoteResult(repDto);
                return new ResponseEntity<Object>(res, HttpStatus.OK);
            }
        }
        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("grant_type", "refresh_token");
        params.add("client_id", clientId);
        params.add("client_secret", secret);
        params.add("refresh_token", refreshTokenCode);
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(params, headers);
        ResponseEntity<Object> response = restTemplate.postForEntity(inOauthServerGetTokenUrl(), requestEntity,
                Object.class);
        RemoteResult res = new RemoteResult(response.getBody());
        return new ResponseEntity<Object>(res, HttpStatus.OK);
    }

    /**
     * 检查JWT令牌是否过期
     * @param token JWT令牌
     * @return 是否过期
     */
    private boolean isTokenExpired(Jwt token) {
        try {
            if (token.getExpiresAt() != null) {
                return token.getExpiresAt().isBefore(java.time.Instant.now());
            }
            return false;
        } catch (Exception e) {
            // 如果无法解析过期时间，默认为已过期
            return true;
        }
    }

    protected String extractTokenKey(String value) {
        if (value == null) { return null; }
        MessageDigest digest;
        try {
            digest = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            throw new IllegalStateException("MD5 algorithm not available.  Fatal (should be in the JDK).");
        }
        try {
            byte[] bytes = digest.digest(value.getBytes("UTF-8"));
            return String.format("%032x", new BigInteger(1, bytes));
        } catch (UnsupportedEncodingException e) {
            throw new IllegalStateException("UTF-8 encoding not available.  Fatal (should be in the JDK).");
        }
    }
}

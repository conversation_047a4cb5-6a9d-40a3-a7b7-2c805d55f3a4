package com.uino.oauth.common.bean;

import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.Set;

import com.uino.bean.permission.business.UserInfo;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.util.Assert;


import lombok.Getter;
import lombok.Setter;

/**
 * 为了应对之后可能出现的用户信息定制化，自己实现用户详情
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
public class UinoUserDetails implements UserDetails {

	private static final long serialVersionUID = 1L;

	/**
	 * 用户信息
	 */
	private UserInfo userInfo;

	/**
	 * 该用户拥有权限的模块codes
	 */
	private Set<String> moduleCodes = new LinkedHashSet<>();

	/**
	 * 用户详情必须填写，只暴漏这一个带限制的构造方法
	 * 
	 * @param userInfo
	 */
	public UinoUserDetails(UserInfo userInfo, Collection<String> moduleCodes) {
		Assert.notNull(userInfo, "用户信息必须填充");
		this.userInfo = userInfo;
		if (moduleCodes != null && moduleCodes.size() > 0) {
			this.moduleCodes.addAll(moduleCodes);
		}
	}

	/**
	 * 获取当前用户权限，现返回所有角色名
	 */
	@Override
	public Collection<SimpleGrantedAuthority> getAuthorities() {
		Set<SimpleGrantedAuthority> auths = new LinkedHashSet<>();
		// 放入角色
		if (userInfo.getRoles() != null) {
			userInfo.getRoles().forEach(role -> {
				auths.add(new SimpleGrantedAuthority("ROLE_" + role.getRoleName()));
			});
		}
		// 放入模块权限
		moduleCodes.forEach(moduleCode -> auths.add(new SimpleGrantedAuthority("MODULECODE_" + moduleCode)));
		return auths;
	}

	/**
	 * 只能获取加密后密码，校对使用，不提供解密
	 */
	@Override
	public String getPassword() {
		// TODO Auto-generated method stub
		return userInfo.getLoginPasswd();
	}

	/**
	 * 对应实际的登录名
	 */
	@Override
	public String getUsername() {
		// TODO Auto-generated method stub
		return userInfo.getLoginCode();
	}

	/**
	 * 用户是否过期，未过期返回true,对应是否锁定字段
	 */
	@Override
	public boolean isAccountNonExpired() {
		return true;
	}

	/**
	 * 用户是否锁定，未锁定返回true,对应是否锁定字段
	 */
	@Override
	public boolean isAccountNonLocked() {
		return !(userInfo.getLockFlag() != null && userInfo.getLockFlag().intValue() == 1);
	}

	/**
	 * 用户凭据是否过期，修改密码会导致过期情况，未过期返回true(暂未实现，现在都认为有效)
	 */
	@Override
	public boolean isCredentialsNonExpired() {
		// TODO Auto-generated method stub
		return true;
	}

	/**
	 * 用户是否被禁用，未禁用返回true,对应用户状态
	 */
	@Override
	public boolean isEnabled() {
		// TODO Auto-generated method stub
		return userInfo.getStatus() != null && userInfo.getStatus().intValue() == 1;
	}

}

package com.uino.oauth.server.web;

import com.uino.api.client.permission.IOauthApiSvc;
import com.uino.bean.permission.business.request.RegisterClientReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.binary.framework.web.RemoteResult;

/**
 * oauth2客户端相关操作
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("client")
public class ClientController {
	@Autowired
	private IOauthApiSvc oauthApi;

	/**
	 * 注册/修改已注册的客户端
	 * 
	 * @param req
	 * @return
	 */
	@PostMapping("register")
	public RemoteResult register(@RequestBody RegisterClientReq req) {
		oauthApi.register(req);
		return new RemoteResult(true);
	}

	/**
	 * 移除客户端
	 * 
	 * @param req
	 * @return
	 */
	@PostMapping("removeClientInfoByCode")
	public RemoteResult removeClientInfoByCode(@RequestBody String req) {
		oauthApi.removeClientInfoByCode(req);
		return new RemoteResult(true);
	}

	/**
	 * 获取所有客户端列表
	 * 
	 * @return
	 */
	@RequestMapping("getList")
	public RemoteResult getList() {
		return new RemoteResult(oauthApi.getAllClents());
	}

}
